'''Class which contains all widgets in GUI window. It is itself placed in MainWindow.
Functions->
UIComponents: adds all widgets to window
attribute_changed: populates list of applicable models from ObjectData.db under specifed constraints
listwidgetdoubleclicked: displays details of model double-clicked in model list
listwidgetclicked: adds selected model to self.shape in diagram or show edit form in edit-model mode (on clicking edit/delete model button)
tfrListClicked: allows transformer in transformer list to be edited if edit/delete transformer button is clicked
extrapolate_cost: extrapolates cost of model with reduced cost, from existing list of models having the same name. Returns dictionary of (model name: polynomial fitted to existing data of cost vs cores)
buttonCloudClicked: displays total cost of deploying all models in diagram to cloud
cloudCostSelected: makes above button visible
dispTotCost: displays total cost of all objects in diagram
dispTotLat: displays total latency of diagram pipeline. Calls Netlatency function in diagram class.
configButtonClicked: form for adding new config to config list
saveConfig: saves aboves form
closeConfigForm: closes above form
gpuchecked: shows options for gpu type in above form, only when gpu option is checked
dispConfigDet: displays details of configuration when double-clicked
addApp: form for adding new application to application list
saveApp: saves above form
closeApp: closes above form
save: saves diagram, applications and configs to csv
load: loads saved diagram
load_helper: helper function for load
'''

from ModelShape import ModelShape
from Shape import Shape
from Arrow import Arrow
from ModelList import ModelList
from StorageShape import StorageShape
from Transformers import TransformerList
import pandas as pd
import difflib
from Config import Config
from PyQt5 import QtGui
from PyQt5.QtGui import QFont
from collections import defaultdict
from PyQt5.QtGui import QStandardItemModel, QStandardItem
from PyQt5.QtCore import QTimer, QPointF
from Diagram import GraphicView
from PyQt5 import QtWidgets
from PyQt5.QtWidgets import QFileDialog,QSizePolicy,QCheckBox
from UniqueModel import UniqueModel
from filemapping import *
from openai import AzureOpenAI
from PyQt5.QtWidgets import *
from collections import deque
from PyQt5 import QtCore
from PyQt5.QtGui import QPixmap, QBrush, QColor,QIcon
from PyQt5.QtCore import Qt, QSize, QTimer, QEvent
import copy
import time
import webbrowser
import numpy as np
import sqlite3
import re
import sys
import subprocess
from bs4 import BeautifulSoup
from MarqueeLabel import MarqueeLabel
#from marq import MarqueeLabel
from numpy.polynomial import Polynomial
import csv
import os
from PyQt5.QtWidgets import QRadioButton
#from pinecone import Pinecone
import PyPDF2
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QTableView, QVBoxLayout, QPushButton,
    QLineEdit, QComboBox, QWidget, QHBoxLayout
)
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import PyPDFLoader
from langchain.vectorstores import Chroma
from langchain_community.embeddings.sentence_transformer import (
    SentenceTransformerEmbeddings,
)
CustomObjectRole = QtCore.Qt.UserRole + 1
from python_terraform import Terraform
from gradio_client import Client
from globalslist import awslist, gcplist, azurelist,questionList #, diagram
db_conn = sqlite3.connect("ObjectData.db")
c = db_conn.cursor()
import boto3
from botocore.exceptions import NoCredentialsError, PartialCredentialsError
from dotenv import load_dotenv
load_dotenv()
api_base = os.getenv("API_BASE")
api_key= os.getenv("AZURE_OPENAI_API_KEY")
deployment_name = os.getenv("DEPLOYMENT_NAME")
api_version = os.getenv("API_VERSION") # this might change in the future
list = []
import subprocess
vpccount=0

#all_chunks = []
deploymentMode = "local"

#global awslist
#awslist = open("awsservices.txt","r").read().splitlines()
#gcplist = open("gcpservices.txt","r").read().splitlines()

class HoverableLabel(QLabel):
    """
    Custom QLabel to handle hover events for changing background color.
    """
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setAlignment(Qt.AlignCenter)
        self.setStyleSheet(
            """
            background-color: white;
            color: black;
            border: none;
            """
        )

    def enterEvent(self, event):
        """
        Handle the event when the mouse enters the label area.
        """
        self.setStyleSheet(
            """
            background-color: lightgray;
            color: black;
            border: none;
            """
        )
        super().enterEvent(event)

    def leaveEvent(self, event):
        """
        Handle the event when the mouse leaves the label area.
        """
        self.setStyleSheet(
            """
            background-color: white;
            color: black;
            border: none;
            """
        )
        super().leaveEvent(event)

class CustomComboButton(QPushButton):
        def __init__(self, parent=None):
            super().__init__(parent)
            self.setText("Help !")
            self.menu = QMenu(self)

            # Connect button to show the menu
            self.clicked.connect(self.show_menu)

        def show_menu(self):
            self.menu.exec_(self.mapToGlobal(self.rect().bottomLeft()))

        def add_label_item(self, label, handler):
            """
            Adds a QLabel as an interactive item in the menu with custom font, padding, and hover effects.
            """
            # Use QWidgetAction to add custom widgets
            action = QWidgetAction(self.menu)

            # Create a container widget for better control
            container_widget = QWidget(self.menu)
            layout = QVBoxLayout(container_widget)
            layout.setContentsMargins(10, 5, 10, 5)  # Adjust padding (left, top, right, bottom)
            layout.setSpacing(0)  # Ensure no additional space around the QLabel
            container_widget.setStyleSheet("background-color: white;")

            # Create HoverableLabel widget
            label_widget = HoverableLabel(label.text(), container_widget)

            # Set font explicitly
            font = label_widget.font()
            font.setPointSize(11)  # Font size
            label_widget.setFont(font)

            # Add QLabel to the layout
            layout.addWidget(label_widget)

            # Embed the container widget into the menu action
            action.setDefaultWidget(container_widget)
            self.menu.addAction(action)

            # Connect the handler if specified
            if handler:
                label_widget.mousePressEvent = lambda event: handler(label_widget)

            # Embed the QLabel into the menu as a custom widget
            action.setDefaultWidget(label_widget)
            self.menu.addAction(action)

class CSWidgets(QWidget):  #Displays all the widgets in Main Window
    #deploymentMode = "local"
    global deploymentMode

    def __init__(self, parent):
        super(CSWidgets, self).__init__(parent)
        #self.extrapolated_cost = self.extrapolate_cost()
        self.sindex = 1
        self.UIComponents()
        self.defaultquery=""
        self.selectedservs=[]
        #self.conversation_history = []
        self.vpcbuttons=[]

    '''def addTableRow(self, table, row_data):
        row = table.rowCount()
        table.setRowCount(row+1)
        col = 0
        for item in row_data:
            cell = QTableWidgetItem(str(item))
            table.setItem(row, col, cell)
            col += 1'''

    def add_label_items(self):
        # Video demo label
        video_label = QLabel('Video Demo')
        video_label.setOpenExternalLinks(True)
        video_label.setAlignment(Qt.AlignCenter)
        self.combo_button.add_label_item(video_label,self.handle_selection)

        # PDF label
        pdf_path = os.path.join(os.getcwd(), "paper.pdf")
        pdf_label = QLabel("Paper")
        pdf_label.setAlignment(Qt.AlignCenter)
        pdf_label.setStyleSheet("color: blue; text-decoration: underline; cursor: pointer;")
        pdf_label.mousePressEvent = lambda event: self.open_file(pdf_path,self.handle_selection)
        self.combo_button.add_label_item(pdf_label,self.handle_selection)

        # Patent label
        patent_path = os.path.join(os.getcwd(), "patent.pdf")
        patent_label = QLabel("Patent")
        patent_label.setAlignment(Qt.AlignCenter)
        patent_label.setStyleSheet("color: blue; text-decoration: underline; cursor: pointer;")
        patent_label.mousePressEvent = lambda event: self.open_file(patent_path)
        self.combo_button.add_label_item(patent_label,self.handle_selection)
        '''
        patent_path = os.path.join(os.getcwd(), "papermain.pdf")
        patent_label = QLabel("Main Paper")
        patent_label.setAlignment(Qt.AlignCenter)
        patent_label.setStyleSheet("color: blue; text-decoration: underline; cursor: pointer;")
        patent_label.mousePressEvent = lambda event: self.open_file(patent_path)
        self.combo_button.add_label_item(patent_label,self.handle_selection)
        '''
        # PPT label
        ppt_path = os.path.join(os.getcwd(), "about.pptx")
        ppt_label = QLabel("Help PPT")
        ppt_label.setAlignment(Qt.AlignCenter)
        ppt_label.setStyleSheet("color: blue; text-decoration: underline; cursor: pointer;")
        ppt_label.mousePressEvent = lambda event: self.open_file(ppt_path)
        self.combo_button.add_label_item(ppt_label,self.handle_selection)






    def handle_selection(self, clicked_label):
        """
        Handle the item selection event. This method is called when a QLabel item is clicked.
        """
        if clicked_label.text().startswith("Video Demo"):
            webbrowser.open("https://youtu.be/WqAcBUT36Xk")
        elif clicked_label.text() == "Paper":
            self.open_file(os.path.join(os.getcwd(), "paper.pdf"))
        elif clicked_label.text() == "Patent":
            self.open_file(os.path.join(os.getcwd(), "patent.pdf"))
        elif clicked_label.text() == "Help PPT":
            self.open_file(os.path.join(os.getcwd(), "about.pptx"))

    def open_file(self,file_path):
        """Opens a file with the default application."""
        if os.path.exists(file_path):
            if os.name == 'nt':  # For Windows
                os.startfile(file_path)
            elif os.name == 'posix':  # For macOS or Linux
                subprocess.run(['open' if sys.platform == 'darwin' else 'xdg-open', file_path])
        else:
            print(f"File not found: {file_path}")


    def help(self):
        print("Got")
        self.formGroupBox1h = QGroupBox()
        self.formGroupBox1h.setWindowTitle("Help !!")
        self.formGroupBox1h.setWindowIcon(QtGui.QIcon('mapleicon.png'))
        self.layout1h = QFormLayout()

        # Add a clickable link for the video demo
        #os.environ["DISPLAY"] = "localhost:10.0"
        self.c11h = QLabel()
        self.c11h.setText('<a href="https://youtu.be/WqAcBUT36Xk">Video Demo</a>')
        self.c11h.setOpenExternalLinks(True)
        self.c11h.setAlignment(Qt.AlignCenter)
        # Connect the link click to open in the default browser
        def open_video_demo():
            webbrowser.open("https://youtu.be/WqAcBUT36Xk")
        self.c11h.linkActivated.connect(open_video_demo)
        self.layout1h.addRow(self.c11h)

        # Add a clickable label for the PDF file
        pdf_path = os.path.join(os.getcwd(), "paper.pdf")
        self.pdf_label = QLabel("Paper")
        self.pdf_label.setAlignment(Qt.AlignCenter)
        self.pdf_label.setStyleSheet("color: blue; text-decoration: underline; cursor: pointer;")
        self.pdf_label.mousePressEvent = lambda event: self.open_file(pdf_path)
        self.layout1h.addRow(self.pdf_label)

        patent_path = os.path.join(os.getcwd(), "patent.pdf")
        self.patent_label = QLabel("Patent")
        self.patent_label.setAlignment(Qt.AlignCenter)
        self.patent_label.setStyleSheet("color: blue; text-decoration: underline; cursor: pointer;")
        self.patent_label.mousePressEvent = lambda event: self.open_file(patent_path)
        self.layout1h.addRow(self.patent_label)

        # Add a clickable label for the PPT file
        ppt_path = os.path.join(os.getcwd(), "about.pptx")
        self.ppt_label = QLabel("Help PPT")
        self.ppt_label.setAlignment(Qt.AlignCenter)
        self.ppt_label.setStyleSheet("color: blue; text-decoration: underline; cursor: pointer;")
        self.ppt_label.mousePressEvent = lambda event: self.open_file(ppt_path)
        self.layout1h.addRow(self.ppt_label)

        # Set the layout and display the form group box
        self.formGroupBox1h.setLayout(self.layout1h)
        self.formGroupBox1h.show()

    def open_table_viewer(self):
        # Example DataFrame
        df = pd.read_excel("noah_arc_combinations.xlsx", sheet_name="Sheet5")

        # Create and show the DataFrame viewer window
        self.viewer = DataFrameViewer(df, self)
        self.viewer.show()

    def UIComponents(self):
        self.grandlayout = QGridLayout()
        #Diagram Window
        global diagram
        diagram = GraphicView()

        self.grandlayout.addWidget(diagram,4,5,40,50)  #Canvas for making pipeline

        #add label for buttons
        #buttonlabel = QLabel("Other Components:")
        #layout.addWidget(buttonlabel,0,2,1,2)

        self.AB = QGroupBox()
        self.AB.setStyleSheet("QGroupBox { border: 1px solid orange; margin : 1px}")
        self.actionHBOX  = QHBoxLayout()

        #add buttons
        global buttonloadArch
        buttonloadArch = QPushButton("Load Architecture")
        buttonloadArch.setToolTip("Open previously saved Architecture.")
        #layout.addWidget(buttonload,0,5,1,1)
        #buttonload.clicked.connect(self.load)
        buttonloadArch.clicked.connect(diagram.loadCanvas)


        global buttonloadImage
        buttonloadImage = QPushButton("Load Image")
        buttonloadImage.setToolTip("Open previously saved Architecture.")
        #layout.addWidget(buttonload,0,5,1,1)
        #buttonload.clicked.connect(self.load)
        buttonloadImage.clicked.connect(diagram.loadImage)

        global buttonloadTemplate
        buttonloadTemplate = QPushButton("Load Template")
        buttonloadTemplate.setToolTip("Open previously saved Architecture.")
        #layout.addWidget(buttonload,0,5,1,1)
        #buttonload.clicked.connect(self.load)
        buttonloadTemplate.clicked.connect(diagram.loadTemplates)


        '''global button1
        button1 = QPushButton()
        button1.setText("Input")
        button1.setToolTip("Click 'Input' button and double-click on canvas \
                           \nto have 'Input' element on canvas.")
        button1.setCheckable(True)
        #layout.addWidget(button1,0,6,1,1)
        button1.clicked.connect(lambda x: diagram.addObject("I",100,50,0,0,""))'''
        global button5
        button5 = QPushButton()
        button5.setText("Connector")
        button5.setToolTip("Click to enter connector mode. Then click on two services to connect them with an arrow.")
        #layout.addWidget(button5,0,7,1,1)
        # Connect the button to the select method to toggle connector mode
        button5.clicked.connect(diagram.select)
        button5.setEnabled(True)

        global button4
        button4 = QPushButton()
        button4.setText("Start")
        button4.setToolTip("Click 'Start' and Double-click on canvas to start drawing.")
        #layout.addWidget(button4,0,8,1,1)
        button4.clicked.connect(self.firstElement)#lambda x: diagram.addObject("T",30,30,0,0,""))
        button4.setEnabled(True)

        '''
        global button2
        button2 = QPushButton()
        #button2.setText("Output")
        button2.setText("Storage")
        button2.setToolTip("Click 'Output' button and double-click on canvas \
                           \nto have 'Output' element on canvas.")
        button2.setCheckable(True)
        button2.setEnabled(False)
        #layout.addWidget(button2,0,8,1,1)
        button2.clicked.connect(lambda x: diagram.addObject("O",100,50,0,0,""))
        '''
        global buttonsave
        buttonsave = QPushButton("Save Architecture")
        buttonsave.setToolTip("Save logic created on canvas for future Architecture.")
        buttonsave.setEnabled(True)
        #layout.addWidget(buttonsave,0,9,1,1)
        #buttonsave.clicked.connect(lambda x: self.save(self.savedapps,widget6))
        buttonsave.clicked.connect(diagram.saveCanvas)
        buttonsave.setEnabled(False)

        global buttonClear
        buttonClear = QPushButton("  Clear Canvas  ")
        buttonClear.setToolTip("Wipe out all elements from canvas.")
        buttonClear.setEnabled(False)
        #layout.addWidget(buttonClear,0,10,1,1)
        buttonClear.clicked.connect(self.buttonClearClicked)

        #global costlatencybutton
        #costlatencybutton = QPushButton("  Total Cost && Latency  ")
        #costlatencybutton.clicked.connect(self.dispTotCostLatency)

        self.actionHBOX.addWidget(buttonloadTemplate)
        self.actionHBOX.addWidget(buttonloadArch)
        self.actionHBOX.addWidget(buttonloadImage)
        self.actionHBOX.addWidget(button4)
        self.actionHBOX.addWidget(button5)  # Add the connector button to the layout
        #self.actionHBOX.addWidget(button2)
        self.actionHBOX.addWidget(buttonsave)
        self.actionHBOX.addWidget(buttonClear)
        global costlatencybutton
        global selectbutton
        #costlatencybutton = QPushButton('Display Cost \n Latency')
        #costlatencybutton = QPushButton('Display Cost && Latency')
        selectbutton = QPushButton('Select')
        selectbutton.setEnabled(True)
        self.actionHBOX.addWidget(selectbutton)


        #global DR
        #DR = QGroupBox()
        #DR.setStyleSheet("QGroupBox { border: 1px solid orange; margin : 1px}")
        #self.mainHBOX  = QHBoxLayout()


        '''size_hint = costlatencybutton.sizeHint()
        costlatencybutton.setMinimumWidth(size_hint.width())
        costlatencybutton.setMaximumWidth(size_hint.width())
        costlatencybutton.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        '''
        selectbutton.clicked.connect(diagram.toggleSelection)

        costlatencybutton = QPushButton('Get Latency and Cost')
        costlatencybutton.setEnabled(False)
        self.actionHBOX.addWidget(costlatencybutton)
        costlatencybutton.clicked.connect(self.dispTotCostLatency)

        tablebutton = QPushButton('Table')
        tablebutton.setEnabled(True)
        tablebutton.clicked.connect(diagram.filter_table)
        self.actionHBOX.addWidget(tablebutton)

        self.combo_button = CustomComboButton()

        self.actionHBOX.addWidget(self.combo_button)
        self.add_label_items()
        # Add the QComboBox to the layout

        self.AB.setLayout(self.actionHBOX)
        self.grandlayout.addWidget(self.AB,1,0,1,45)
        #global deploybutton
        #deploybutton = QPushButton()
        #deploybutton.setText("Deploy")
        #deploybutton.setToolTip("Deploy architecture created on canvas on cloud.")
        #deploybutton.setEnabled(False)
        #deploybutton.clicked.connect(self.deployArch)

        #self.mainHBOX.addWidget(costlatencybutton)
        #self.mainHBOX.addWidget(deploybutton)
        #DR.setLayout(self.mainHBOX)
        #self.grandlayout.addWidget(DR,1,7,1,6)
        #layout.addWidget(QLabel("                                                                                             "),1,6,1,2)
        #layout.addWidget(QLabel("                                                                                             "),1,5,1,1)

        global perfDisplay
        perfDisplay = QGroupBox()
        perfDisplay.setStyleSheet("QGroupBox { border: 1px solid orange; margin : 1px}")
        self.subHBOX = QGridLayout()#QHBoxLayout()
        self.subVBOX1 = QGroupBox()
        self.subVBOX2 = QGroupBox()

        global totCostDisp
        global totLateDisp
        totCostDisp = QLabel("-- $")
        totLateDisp = QLabel("-- sec")
        totCostName = QLabel("Cost: ")
        totLateName = QLabel("Latency: ")

        self.subHBOX.addWidget(totCostName,0,0)
        self.subHBOX.addWidget(totCostDisp,0,1)
        self.subHBOX.addWidget(totLateName,1,0)
        self.subHBOX.addWidget(totLateDisp,1,1)

        #self.subVBOX1.addItem(totCostName)
        #self.subVBOX1.addWidget(totCostDisp)
        #self.subVBOX2.addWidget(totLateName)
        #self.subVBOX2.addWidget(totLateDisp)

        #self.subHBOX.addWidget(totCostName)
        #self.subHBOX.addWidget(totCostDisp)
        #self.subHBOX.addWidget(totLateName)
        #self.subHBOX.addWidget(totLateDisp)
        #self.subHBOX.addWidget(self.subVBOX1)
        #self.subHBOX.addWidget(self.subVBOX2)
        perfDisplay.setLayout(self.subHBOX)
        self.grandlayout.addWidget(perfDisplay,1,45,1,2)




        self.configBox = QGroupBox()
        self.configBox.setStyleSheet("QGroupBox { border: 1px solid orange; margin : 1px}")
        self.cnfBOX  =  QHBoxLayout()


        global realdomainsBox
        self.realdomainsBox = QGroupBox()
        self.realdomainsBox.setStyleSheet("QGroupBox { border: 1px solid orange; margin : 1px}")
        rdBOX  = QVBoxLayout()

        rdBOX.addWidget(QLabel("Applicable Domain:"))

        global combobox1
        combobox1 = QComboBox()
        appdomains = [ "IoT","GenAI","Compute","Machine Learning & AI","Aerospace", "Agriculture", "Automotive", "Consumer Packaged Goods", "Defence", "Digital Marketing", "Education", "Electronics", "Energy", "Financial Services", "Game Tech", "Healthcare", "Hospitality", "Life Sciences", "Manufacturing", "Media & Entertainment", "Power & Utilities", "Retail & Wholesale", "Semiconductors", "Telecommunications", "Transportation & Logistics", "Travel" ]

        combobox1.addItems(appdomains)
        combobox1.currentIndexChanged.connect(self.updatePlaceholder)
        rdBOX.addWidget(combobox1)
        self.realdomainsBox.setLayout(rdBOX)
        self.realdomainsBox.setEnabled(True)
        self.grandlayout.addWidget(self.realdomainsBox,6,0,2,5)


        global domainsBox
        self.domainsBox = QGroupBox()
        self.domainsBox.setStyleSheet("QGroupBox { border: 1px solid orange; margin : 1px}")
        dBOX  = QVBoxLayout()

        #add Label
        label0 = QLabel("Cloud Service Providers:")

        #add Checkboxes for application domains #global csplist #csplist =         QListWidget()
        #domain = ["AWS","GCP","Azure"] #for i in         range(len(domain)):
        #    item = QtWidgets.QListWidgetItem(domain[i])
            #item.setToolTip(domain[i])
            #item.setFlags(QtCore.Qt.ItemIsUserCheckable |            QtCore.Qt.ItemIsEnabled)
        #    item.setCheckState(QtCore.Qt.Unchecked) csplist.addItem(item)
        #csplist.itemClicked.connect(self.populateCSPservices)

        global combocsp
        combocsp = QComboBox()
        csps = [ "AWS","GCP","AZURE"]
        combocsp.addItems(csps)
        combocsp.currentIndexChanged.connect(self.populateCSPservices)
        dBOX.addWidget(label0)
        dBOX.addWidget(combocsp)
        #dBOX.addWidget(csplist)
        self.domainsBox.setLayout(dBOX)
        #layout.addWidget(self.domainsBox,7,0,2,2)

        #add Application #addapp = QPushButton("Add Application")
        #layout.addWidget(addapp,3,1,1,1) #addapp.clicked.connect(self.addApp)

        #savedappslabel = QLabel("Saved Applications:")
        #layout.addWidget(savedappslabel,3,2,1,1)
        self.savedapps = QListWidget()
        #layout.addWidget(self.savedapps,4,2,1,2)
        self.savedapps.itemDoubleClicked.connect(self.listwidgetdoubleclicked)

        #add Label label1 = QLabel("Model Name:")
        #layout.addWidget(label1,5,0,1,4)

        #add Checkboxes for model names
        global modellist
        modellist = QListWidget()
        #layout.addWidget(modellist,6,0,1,4)
        c.execute('SELECT Model FROM models')
        for modelName in c:
            if(len(modellist.findItems(modelName[0],QtCore.Qt.MatchExactly)) == 0):
                item = QtWidgets.QListWidgetItem(modelName[0])
                item.setFlags(QtCore.Qt.ItemIsUserCheckable | QtCore.Qt.ItemIsEnabled)
                item.setCheckState(QtCore.Qt.Unchecked)
                modellist.addItem(item)
        #modellist.itemClicked.connect(self.attribute_changed)

        modellist.setFixedHeight(100)
        self.target = QGroupBox()
        self.target.setStyleSheet("QGroupBox { border: 1px solid orange; margin : 1px}")
        self.T1BOX  = QHBoxLayout()
        self.T2BOX  = QHBoxLayout()
        self.TBOX  = QHBoxLayout()#QVBoxLayout()

        #add Label
        label2 = QLabel("Latency Bound:")
        #layout.addWidget(label2,1,0,1,1)#4)

        #add SpinBox for latency constraint
        global widget3
        widget3 = QSpinBox()
        widget3.setMinimum(0)
        widget3.setMaximum(999)
        widget3.setSingleStep(1)
        widget3.setValue(999999)
        widget3.setSuffix(" s")
        #widget3.editingFinished.connect(self.attribute_changed)
        #layout.addWidget(widget3,8,0,1,4) #layout.addWidget(widget3,1,1,1,1)
        self.T1BOX.addWidget(label2)
        self.T1BOX.addWidget(widget3)

        #add Label
        label3 = QLabel("Cost/Inference :")
        #layout.addWidget(label3,9,0,1,4) #layout.addWidget(label3,2,0,1,1)

        #add SpinBox for cost constraint
        global widget4
        widget4 = QSpinBox()
        widget4.setMinimum(0)
        widget4.setMaximum(99999)
        widget4.setSingleStep(10)
        widget4.setValue(1000)
        widget4.setSuffix(" $")
        #widget4.editingFinished.connect(self.attribute_changed)
        #layout.addWidget(widget4,10,0,1,4) #layout.addWidget(widget4,2,1,1,1)
        self.T2BOX.addWidget(label3)
        self.T2BOX.addWidget(widget4)

        #self.TBOX.addLayout(self.T1BOX) #self.TBOX.addLayout(self.T2BOX)

        generateButton = QPushButton("Generate")
        #generateButton.clicked.connect(self.LoadDAGs)

        self.TBOX.addWidget(label2)
        self.TBOX.addWidget(widget3)
        self.TBOX.addWidget(label3)
        self.TBOX.addWidget(widget4)
        self.TBOX.addWidget(generateButton)

        self.target.setLayout(self.TBOX)
        self.target.setVisible(False)
        #layout.addWidget(self.target,1,9,1,5)

        global modelsBox
        modelsBox = QGroupBox()
        modelsBox.setStyleSheet("QGroupBox { border: 1px solid orange; margin : 1px } ")
        self.MHBOX  = QHBoxLayout()
        self.MHBOX1 = QHBoxLayout()
        self.MBOX  = QVBoxLayout()

        #add Label
        label4 = QLabel("Cloud Services for CSP :")
        #layout.addWidget(label4,4,0,1,1)

        #List for displaying applicable models
        global widget5
        widget5 = QListWidget()
        for item in awslist[1:]:
            widget5.addItem(item)
        #layout.addWidget(widget5,5,0,7,2)
        widget5.itemDoubleClicked.connect(self.listwidgetdoubleclicked)
        #widget5.itemClicked.connect(self.listwidgetclicked)
        widget5.itemClicked.connect(self.listServiceClicked)
        #widget5.setEnabled(False)
        widget5.setFixedHeight(200) #--->

        global button6
        button6 = QPushButton("Add Model")
        button6.setToolTip("Add new model in database.")
        #button6.setEnabled(False) #self.allmodels = ModelList(widget5)
        #layout.addWidget(button6,12,0,1,1)
        #button6.clicked.connect(self.allmodels.addModel)

        ''' global button7 button7 = QPushButton("Edit/Delete Model")
        button7.setToolTip("Edit details of existing model.")
        button7.setCheckable(True) button7.setEnabled(False)
        #layout.addWidget(button7,12,1,1,1)
        #button7.clicked.connect(self.allmodels.view) '''
        self.le_search = QLineEdit()
        self.le_search.returnPressed.connect(self.find_item)
        searchbutton = QPushButton("Search")
        searchbutton.clicked.connect(self.find_item)

        #def find_item(self):
        #    out = self.listwidget.findItems(self.le_search.text(),
        #    Qt.MatchContains | Qt.MatchCaseSensitive)      # +++
        #    print("out->", [ i.text() for i in out ] )


        #self.MHBOX.addWidget(button6) #self.MHBOX.addWidget(button7)
        self.MHBOX.addWidget(self.le_search)
        self.MHBOX.addWidget(searchbutton)

        self.MHBOX1.addWidget(label4)
        self.MHBOX1.addWidget(combocsp)
        #self.MBOX.addWidget(label4)
        self.MBOX.addLayout(self.MHBOX1)
        self.MBOX.addWidget(widget5)
        self.MBOX.addLayout(self.MHBOX)

        modelsBox.setLayout(self.MBOX)
        modelsBox.setEnabled(False)
        modelsBox.setFixedHeight(280)  # Increased height for better visibility
        self.grandlayout.addWidget(modelsBox,13,0,3,5)


        #global postDeploymentBox
        self.postDeploymentBox = QGroupBox()
        self.postDeploymentBox.setStyleSheet("QGroupBox { border: 1px solid orange; margin : 1px } ")

        getRecommendationbutton = QPushButton("Current\nDeployment\nStatus")
        getRecommendationbutton.clicked.connect(self.get_trusted_advisor_recommendations)

        self.depBox  = QHBoxLayout()
        self.depBox.addWidget(getRecommendationbutton)

        optimizeArch = QPushButton("Optimize\nArchitecture")
        optimizeArch.clicked.connect(diagram.getworkload)
        self.depBox.addWidget(optimizeArch)

        self.postDeploymentBox.setLayout(self.depBox)
        self.grandlayout.addWidget(self.postDeploymentBox,28,0,1,5) #--->

        #-------------------------------------------------------

        #global RAGinputsBox
        self.ragInputsBox = QGroupBox()
        self.ragInputsBox.setStyleSheet("QGroupBox { border: 1px solid orange; margin : 1px } ")
        '''
        getLLMbutton = QPushButton("LLM")
        getLLMbutton.clicked.connect(self.tempfunc1)
        getVDBbutton = QPushButton("VDB")
        getVDBbutton.clicked.connect(self.tempfunc2)


        self.ragBox  = QHBoxLayout()
        self.ragBox.addWidget(getLLMbutton)
        self.ragBox.addWidget(getVDBbutton)

        self.ragInputsBox.setLayout(self.ragBox)
        self.grandlayout.addWidget(self.ragInputsBox,15,0,1,2)
        '''
        #-------------------------------------------------------

        global postDeploymentBox3
        postDeploymentBox3 = QGroupBox()
        postDeploymentBox3.setStyleSheet("QGroupBox { border: 1px solid orange; margin : 1px } ")
        #postDeploymentBox3.setFixedSize(300, 50)
        hidetop=QPushButton("^")
        hidetop.clicked.connect(self.dohidetop)

        showtop=QPushButton("V")
        showtop.clicked.connect(self.doshowtop)


        hidetop.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        showtop.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)


        hidetop.setFixedSize(2, 30)
        showtop.setFixedSize(2, 30)



        self.depBox2  = QHBoxLayout()
        self.depBox2.addWidget(hidetop)
        self.depBox2.addWidget(showtop)
        self.depBox2.setContentsMargins(2, 2, 2, 2)
        #self.depBox2.setSpacing(0)

        postDeploymentBox3.setLayout(self.depBox2)
        self.grandlayout.addWidget(postDeploymentBox3,3,0,1,5)

        #----------------------------------------------------------------------------

        global postDeploymentBox4
        postDeploymentBox4 = QGroupBox()
        postDeploymentBox4.setStyleSheet("QGroupBox { border: 1px solid orange; margin : 1px } ")
        #postDeploymentBox3.setFixedSize(300, 50)


        hide=QPushButton("<")
        hide.clicked.connect(self.dohide)

        show=QPushButton(">")
        show.clicked.connect(self.doshow)

        hide.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        show.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)


        hide.setFixedSize(2, 30)
        show.setFixedSize(2, 30)

        self.depBox  = QHBoxLayout()
        self.depBox.addWidget(hide)
        self.depBox.addWidget(show)
        self.depBox.setContentsMargins(2, 2, 2, 2)
        #self.depBox.setSpacing(0)


        postDeploymentBox4.setLayout(self.depBox)
        self.grandlayout.addWidget(postDeploymentBox4,4,0,1,5)

        #----------------------------------------------------------------------------

        global postDeploymentBox2
        postDeploymentBox2 = QGroupBox()
        postDeploymentBox2.setStyleSheet("QGroupBox { border: 1px solid orange; margin : 1px } ")



        getRecommendationbutton = QPushButton("Create new VPC")
        getRecommendationbutton.clicked.connect(self.generateVPC)

        global vpcdepbox
        vpcdepbox  = QHBoxLayout()
        vpcdepbox.addWidget(getRecommendationbutton)
        postDeploymentBox2.setLayout(vpcdepbox)
        self.grandlayout.addWidget(postDeploymentBox2,3,5,1,40)

        optimizeArch = QPushButton("Edit VPC")
        optimizeArch.clicked.connect(self.editVPC)
        vpcdepbox.addWidget(optimizeArch)
        #-------------------------------------------------------
        self.storageBox = QGroupBox()
        self.storageBox.setStyleSheet("QGroupBox { border: 1px solid orange; margin : 1px } ")
        self.MHBOX  = QHBoxLayout()
        self.MBOX  = QVBoxLayout()

        #add Label
        storageBoxLabel = QLabel("Cloud Storages:")
        #layout.addWidget(label4,4,0,1,1) #List for displaying applicable
        global storageWidget
        storageWidget = QListWidget()
        #layout.addWidget(widget5,5,0,7,2)
        # storageWidget.itemDoubleClicked.connect(self.listwidgetdoubleclicked)
        # storageWidget.itemClicked.connect(self.storageListWidgetClicked)
        # storageWidget.setEnabled(False)
        storageWidget.setFixedHeight(100)


        self.formGroupBox = QGroupBox()
        self.formGroupBox.setStyleSheet("QGroupBox { border: 1px solid orange; margin : 1px }")
        self.formGroupBox.setWindowTitle("Get the cloud configuration")
        self.formGroupBox.setWindowIcon(QtGui.QIcon('mapleicon.png'))
        self.layout = QFormLayout()

        self.system_prompt=None
        labelllama = QLabel("Connect with AI:")
        global t1
        #t1 = QTextEdit()
        red_text = combobox1.currentText()
        #t1.setText("Enter business requirements here for "+combobox1.currentText()+" domain")
        #t1.setHtml(f" Enter business requirements here for <span style=\"color:#ff0000;\" > {red_text} </span> domain")
        self.defaultquery="Enter business requirements here for "+red_text+" domain."
        #t1.textChanged.connect(self.askingQuery)

        #global t2
        self.t2 = QTextEdit()
        #tbox = QHBoxLayout()
        #tbox.addWidget(t1)
        #tbox.addWidget(self.t2)
        #t2.setText("AWS IoT Core\nAWS Greengrass\nAmazon SageMaker\nAmazon Redshift\nAmazon Kinesis")
        rfp_button = QPushButton("Upload\nRequirement\nFile")
        rfp_button.clicked.connect(self.uploadprompt)
        rfp_button.setFixedWidth(200)
        submit_button = QPushButton("Submit\nWritten\nRequirement")
        submit_button.clicked.connect(self.expandArea)
        submit_button.setFixedWidth(200)
        #expand_button = QPushButton("Expand\nResult\nServices")
        # expand_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        # expand_button.setFixedWidth(2)
        #expand_button.setIcon(QIcon('expand.png'))
        #expand_button.clicked.connect(self.expandResultArea)
        #draw_button = QPushButton("Create\nArch\nGraph")
        #draw_button.clicked.connect(self.plotGraph)
        #questionnaire_button = QPushButton("Questions")
        #questionnaire_button.clicked.connect(self.displayQuestions)
        #cancel_button.clicked.connect(lambda x: diagram.addObject("I1",200,50,0,0,""))
        #cancel_button.clicked.connect(lambda x: diagram.addObject("I2",100,100,0,0,""))

        bbox = QHBoxLayout()
        bbox.addWidget(rfp_button)
        bbox.addWidget(submit_button)
        #bbox.addWidget(expand_button)
        #bbox.addWidget(draw_button)
        #bbox.addWidget(questionnaire_button)

        self.layout.addRow(labelllama)

        self.layout.addRow(bbox)
        #labelresult = QLabel("Result Services:")
        #self.layout.addRow(labelresult)
        #self.layout.addRow(tbox)
        self.formGroupBox.setLayout(self.layout)
        self.grandlayout.addWidget(self.formGroupBox,6,0,5,5)



        global labelmarquee
        labelmarquee = QLabel()#(" Tip :: Use 'Input' button(& double click on canvas) to add 'Input' element in canvas or Use 'Load Logic' button to load previously saved logic.")
        labelmarquee.setStyleSheet("QLabel {margin : 1px}")
        #layout.addWidget(labelmarquee,0,3,1,15)
        labelmarquee.setFont(QFont("Arial",25))
        labelmarquee = MarqueeLabel(self) #labelmarquee.setText("A B C D E")
        labelmarquee.setSpeed(1)
        labelmarquee.setDirection(Qt.RightToLeft)
        labelmarquee.setText(" :: Use 'Start' button(& double click on canvas) to add 'User' element in canvas or Use 'Load Architecture' button to load previously saved Architecture. \ :: Select Domain as per requirement and use 'Connect with AI' feature to identify the most suitable cloud services for your requirements.")
        self.marquee = QGroupBox()
        self.marquee.setStyleSheet("QGroupBox { border: 1px solid orange; margin : 1px }")
        marqlayout = QHBoxLayout()
        marqlayout.addWidget(labelmarquee)
        self.marquee.setLayout(marqlayout)
        # layout.addWidget(self.marquee,2,0,1,20)

        global label8
        label8 = QLabel(" Applied Config ::")
        #layout.addWidget(label8,18,3,1,10)
        label8.setFont(QFont("Arial",12))

        global buttoncloud
        buttoncloud = QPushButton("Calculate Cost")
        self.grandlayout.addWidget(buttoncloud,20,6,1,2)
        buttoncloud.clicked.connect(self.buttonCloudClicked)
        buttoncloud.setVisible(False)


# =============================================================================
# #buttonconfig = QPushButton("Add New Server Config") global buttonconfig
# buttonconfig = QPushButton("Add Server")
# layout.addWidget(buttonconfig,20,9,1,1)
# buttonconfig.clicked.connect(self.configButtonClicked)
# if(deploymentMode=="cloud"): buttonconfig.setVisible(False)
#
#         global serverlabel serverlabel = QLabel("Saved Configurations:")
#         layout.addWidget(serverlabel,21,9,1,1) if(deploymentMode=="cloud"):
#         serverlabel.setVisible(False)
#         =============================================================================

        self.setLayout(self.grandlayout)


    #Functions
    def dohide(self):
        # Hide the sidebar and expand the canvas to occupy the available space.
        self.formGroupBox.setVisible(False)
        self.realdomainsBox.setVisible(False)
        modelsBox.setVisible(False)
        self.postDeploymentBox.setVisible(False)
        # Do not hide postDeploymentBox4 to keep the < and > buttons visible

        # Adjust the canvas to occupy the remaining space
        self.grandlayout.addWidget(diagram, 4, 0, 40, 50)  # Expand canvas horizontally

    def doshow(self):
        # Show the sidebar and adjust the canvas size accordingly.
        self.formGroupBox.setVisible(True)
        self.realdomainsBox.setVisible(True)
        modelsBox.setVisible(True)
        self.postDeploymentBox.setVisible(True)
        # Ensure postDeploymentBox4 remains visible
        postDeploymentBox4.setVisible(True)

        # Adjust the canvas to fit alongside the sidebar
        self.grandlayout.addWidget(diagram, 4, 5, 40, 45)  # Adjust canvas size

    def dohidetop(self):
        # Hide the top panel and expand the canvas vertically without affecting the sidebar or its background.
        self.AB.setVisible(False)
        perfDisplay.setVisible(False)
        postDeploymentBox2.setVisible(False)

        # Ensure the sidebar and its background remain visible
        postDeploymentBox4.setVisible(True)
        self.sidebar.setStyleSheet("background-color: #f0f0f0;")  # Example background color

        # Adjust the canvas to occupy the full top space without affecting the sidebar
        self.grandlayout.addWidget(diagram, 0, 5, 45, 42)  # Expand canvas vertically

    def doshowtop(self):
        # Show the top panel and adjust the canvas size accordingly without affecting the sidebar.
        self.AB.setVisible(True)
        perfDisplay.setVisible(True)
        postDeploymentBox2.setVisible(True)

        # Ensure the sidebar remains unaffected
        postDeploymentBox4.setVisible(True)

        # Adjust the canvas to fit below the top panel
        self.grandlayout.addWidget(diagram, 4, 5, 40, 45)  # Adjust canvas size

    # Ensure the layout updates dynamically
    def resizeEvent(self, event):
        # Handle window resize events to ensure the layout remains responsive.
        super(CSWidgets, self).resizeEvent(event)
        self.grandlayout.update()

    def createVPC(self):
        self.formGroupBoxv2 = QGroupBox()
        self.formGroupBoxv2.setMaximumSize(800, 350)  # Adjusted maximum size for a better fit
        self.formGroupBoxv2.setMinimumSize(200, 100)  # Set a minimum size to ensure content fits
        #binary_group = QButtonGroup(self)
        question_layout = QVBoxLayout()
        heading = QLabel("Please enter which services are in the VPC.")

        question_layout.addWidget(heading)
        wk=[]
        self.radio_widgetservs = QWidget(self)
        for item in diagram.scene.items():
            if isinstance(item,Shape):
                wk.append(item.name)
        checkboxes_widget = QWidget(self)
        checkbox_layout = QVBoxLayout(checkboxes_widget)

        # Create "Select All" checkbox
        select_all_checkbox = QCheckBox("Select All")
        checkbox_layout.addWidget(select_all_checkbox)

        # Create individual checkboxes for each service
        service_checkboxes = []
        for serv in wk:
            #if serv != "Amazon Elastic Kubernetes Services" and serv != "Elastic Load Balancing" and serv != "User":
            service_checkbox = QCheckBox(serv)
            service_checkboxes.append(service_checkbox)  # Store checkbox in dictionary
            checkbox_layout.addWidget(service_checkbox)

        question_layout.addWidget(checkboxes_widget)

        # Connect "Select All" checkbox to toggle other checkboxes
        select_all_checkbox.toggled.connect(lambda checked: self.toggle_all(checked, service_checkboxes))


        # Create buttons
        ok_button = QPushButton("Okay")
        cancel_button = QPushButton("Cancel")
        ok_button.clicked.connect(self.generateVPC)
        cancel_button.clicked.connect(self.returner2)

        # Horizontal layout for buttons
        HBox = QHBoxLayout()
        HBox.addWidget(ok_button)
        HBox.addWidget(cancel_button)
        h_layout_widget = QWidget()
        h_layout_widget.setLayout(HBox)

        question_layout.addWidget(h_layout_widget)

        # Set spacing and margins for the layout to make it compact
        question_layout.setContentsMargins(5, 5, 5, 5)  # Reduce margins
        question_layout.setSpacing(5)  # Reduce spacing

        self.formGroupBoxv2.setLayout(question_layout)
        self.formGroupBoxv2.show()

    def returner2(self):
        self.formGroupBoxv2.close()
    def generateVPC(self):

        global vpccount
        vpccount+=1
        '''
        checkboxes = self.formGroupBoxv2.findChildren(QCheckBox)
        checked_checkboxes = [checkbox.text() for checkbox in checkboxes if checkbox.isChecked()]
        if checked_checkboxes is not None:
            answer = checked_checkboxes
        else:
            answer = []
            #count+=1
        print(answer)
        '''
        items = diagram.scene.items()
        #self.selectedservs=[]
        selected_service_objects = []
        selected_service_names = []
        for item in items:
            if isinstance(item,Shape):
               item.proxy.setVisible(False)
            if isinstance(item,Shape)  and item.checkbox.isChecked():
               # Store the actual service object instead of just the name
               selected_service_objects.append(item)
               selected_service_names.append(item.name)
               item.checkbox.setChecked(False)

        print(f"Selected services for VPC {vpccount}: {selected_service_names}")

        optimizeArch = QPushButton("VPC "+str(vpccount))
        if vpccount==1:
            optimizeArch.setStyleSheet("background-color: red;")
        elif vpccount==2:
            optimizeArch.setStyleSheet("background-color: blue;")
        elif vpccount==3:
            optimizeArch.setStyleSheet("background-color: green;")
        elif vpccount==4:
            optimizeArch.setStyleSheet("background-color: pink;")
        diagram.getvpcdetails(optimizeArch)
        optimizeArch.clicked.connect(lambda: diagram.getvpcdetails(optimizeArch))
        self.vpcbuttons.append(optimizeArch)
        global vpcdepbox
        vpcdepbox.addWidget(optimizeArch)

        # Use the new function that works with service objects
        diagram.drawsquareByObjects(selected_service_objects, vpccount)
        self.vpcbuttons.append(optimizeArch)
        diagram.vpcbuttons.append(optimizeArch)
        #self.formGroupBoxv2.close()



    def editVPC(self):
        self.formGroupBoxv4 = QGroupBox()
        self.formGroupBoxv4.setMaximumSize(800, 350)  # Adjusted maximum size for a better fit
        self.formGroupBoxv4.setMinimumSize(200, 100)  # Set a minimum size to ensure content fits

        question_layout = QVBoxLayout()
        heading = QLabel("Please select the VPC.")
        question_layout.addWidget(heading)

        # Collect service names (as before)
        wk = []
        self.radio_widgetservs = QWidget(self)
        for v in range(vpccount):
            wk.append("VPC "+str(v+1))

        # Create the layout for the radio buttons
        radio_widget = QWidget(self)
        radio_layout = QVBoxLayout(radio_widget)

        # Create a QButtonGroup to group radio buttons together
        button_group = QButtonGroup(self)

        # Create individual radio buttons for each service
        service_radio_buttons = []
        for serv in wk:
            # Create a radio button for each service
            radio_button = QRadioButton(serv)
            service_radio_buttons.append(radio_button)  # Store radio button in list

            # Add the radio button to the layout
            radio_layout.addWidget(radio_button)

            # Add the radio button to the button group
            button_group.addButton(radio_button)

        # Add the radio button widget to the main layout
        question_layout.addWidget(radio_widget)

        # Optionally, connect to a slot if you need to handle the selection change
        button_group.buttonClicked.connect(self.editparticularVPC)

        # Set the layout for the group box
        self.formGroupBoxv4.setLayout(question_layout)  # Changed from formGroupBoxv3 to formGroupBoxv4
        self.formGroupBoxv4.show()

    def editparticularVPC(self,button: QRadioButton):
    # Get the text (label) of the selected radio button
        self.editvpc = int(button.text().split(" ")[-1])
        self.formGroupBoxv4.close()
        self.editvpc=1
        self.formGroupBoxv3 = QGroupBox()
        self.formGroupBoxv3.setMaximumSize(800, 350)  # Adjusted maximum size for a better fit
        self.formGroupBoxv3.setMinimumSize(200, 100)  # Set a minimum size to ensure content fits
        #binary_group = QButtonGroup(self)
        question_layout = QVBoxLayout()
        heading = QLabel("Please enter which services are in the VPC.")

        question_layout.addWidget(heading)
        wk=[]
        self.radio_widgetservs = QWidget(self)
        for item in diagram.scene.items():
            if isinstance(item,Shape):
                wk.append(item.name)
        wk.append("None")
        checkboxes_widget = QWidget(self)
        checkbox_layout = QVBoxLayout(checkboxes_widget)

        # Create "Select All" checkbox
        select_all_checkbox = QCheckBox("Select All")
        checkbox_layout.addWidget(select_all_checkbox)

        # Create individual checkboxes for each service
        service_checkboxes = []
        for serv in wk:
            #if serv != "Amazon Elastic Kubernetes Services" and serv != "Elastic Load Balancing" and serv != "User":
            service_checkbox = QCheckBox(serv)
            service_checkboxes.append(service_checkbox)  # Store checkbox in dictionary
            checkbox_layout.addWidget(service_checkbox)

        question_layout.addWidget(checkboxes_widget)

        # Connect "Select All" checkbox to toggle other checkboxes
        select_all_checkbox.toggled.connect(lambda checked: self.toggle_all(checked, service_checkboxes))


        # Create buttons
        ok_button = QPushButton("Okay")
        cancel_button = QPushButton("Cancel")
        ok_button.clicked.connect(self.changeVPC)
        cancel_button.clicked.connect(self.returner2)

        # Horizontal layout for buttons
        HBox = QHBoxLayout()
        HBox.addWidget(ok_button)
        HBox.addWidget(cancel_button)
        h_layout_widget = QWidget()
        h_layout_widget.setLayout(HBox)

        question_layout.addWidget(h_layout_widget)

        # Set spacing and margins for the layout to make it compact
        question_layout.setContentsMargins(5, 5, 5, 5)  # Reduce margins
        question_layout.setSpacing(5)  # Reduce spacing

        self.formGroupBoxv3.setLayout(question_layout)
        self.formGroupBoxv3.show()


    def changeVPC(self):

        checkboxes = self.formGroupBoxv3.findChildren(QCheckBox)
        checked_checkboxes = [checkbox.text() for checkbox in checkboxes if checkbox.isChecked()]
        if checked_checkboxes is not None:
            answer = checked_checkboxes
        else:
            answer = []
            #count+=1
        print("inchangevpc")
        print(f"Selected services for VPC edit: {answer}")
        self.formGroupBoxv3.close()

        # Use the new function that works with service names (for backward compatibility)
        diagram.deletesquares(answer, self.editvpc)

    def extract_recommended_action(self, description):
        # Use regular expressions to find the part of the text between "Recommended Action" and "Additional Resources"
        match = re.search(r'Recommended Action(.*?)Additional Resources', description, re.DOTALL)
        if match:
            recommended_action_html = match.group(1).strip()
            # Remove HTML tags and links
            soup = BeautifulSoup(recommended_action_html, "html.parser")
            recommended_action_text = soup.get_text(separator=" ").strip()
            return recommended_action_text
        return "N/A"

    def get_trusted_advisor_recommendations(self):
        try:
            font = QFont('Arial', 11)#, QFont.Bold)
            #self.setWindowTitle("Recommendation")
            #self.setGeometry(self.left, self.top, self.width, self.height)
            self.formGroupBox = QGroupBox()
            # Create a table widget
            self.table_widget = QTableWidget()
            self.table_widget.setRowCount(50)
            self.table_widget.setColumnCount(4)
            self.table_widget.setHorizontalHeaderLabels(['Counter', 'Check ID', 'Check Name', 'Check Category'])
            #setCentralWidget(self.table_widget)

            # Create the tree widget
            self.tree_widget = QTreeWidget()
            self.tree_widget.setColumnCount(5)
            self.tree_widget.setHeaderLabels(['Check Name', 'Category', 'Status', 'Metadata', 'Recommended Action'])
            header = self.tree_widget.header()

            # Set the layout
            #self.layout = QVBoxLayout(self.table_widget)
            #layout.addWidget(table_widget)
            #container = QWidget()
            #container.setLayout(layout)
            #self.setCentralWidget(container)
            #self.formGroupBox.layout().addWidget(self.table_widget)
            #self.formGroupBox.setFixedWidth(700)
            #self.formGroupBox.setFixedHeight(500)
            #self.formGroupBox.setLayout(self.layout)


            #Initialize a session using Amazon Trusted Advisor
            client = boto3.client('support', region_name='us-east-1')

            # Get the list of all checks
            response = client.describe_trusted_advisor_checks(language='en')
            checks = response['checks']

            # Iterate over each check to get the detailed result
            counter=0
            recommendations = []
            for check in checks:
                #counter = counter + 1
                check_id = check['id']
                check_name = check['name']
                check_category = check['category']
                if check_category in ['cost_optimizing', 'performance']:
                    #print(str(counter)+" : "+check_id+ " : "+check_name+" : "+check_category)
                    #counter = counter + 1
                    #self.table_widget.setItem(counter-1, 0, QTableWidgetItem(str(counter)))
                    #self.table_widget.setItem(counter-1, 1, QTableWidgetItem(check_id))
                    #self.table_widget.setItem(counter-1, 2, QTableWidgetItem(check_name))
                    #self.table_widget.setItem(counter-1, 3, QTableWidgetItem(check_category))

                    # Get detailed information for each check
                    check_result = client.describe_trusted_advisor_check_result(
                        checkId=check_id,
                        language='en'
                    )

                    # Check if 'flaggedResources' is present in the result
                    if 'result' in check_result and 'flaggedResources' in check_result['result']:
                        recommended_action = self.extract_recommended_action(check['description'])
                        for resource in check_result['result']['flaggedResources']:
                            recommendation = {
                                'Check Name': check_name,
                                'Category': check_category,
                                'Status': resource['status'],
                                'Recommended Action': recommended_action  # Extracted Recommended Action
                            }
                            # Only add metadata if it is present
                            if 'metadata' in resource:
                                # Replace commas with newline characters in metadata
                                metadata = '\n'.join(str(item) if item is not None else 'N/A' for item in resource['metadata'])
                                if check_name == 'Low Utilization Amazon EC2 Instances':
                                    metadata = "Avg CPU Util "+resource['metadata'][19]
                                    recommendation['Metadata'] = metadata
                                    recommendation['Recommended Action'] = resource['metadata'][3]+" :: replace by new configuration <XYZ> and save $$ monthly"
                                else:
                                    #print("Error Coming")
                                    #print(metadata)
                                    recommendation['Metadata'] = metadata
                            recommendations.append(recommendation)

            # Add data to the tree
            check_dict = {}
            for recommendation in recommendations:
                check_name = recommendation['Check Name']
                if check_name not in check_dict:
                    check_dict[check_name] = QTreeWidgetItem([check_name])
                    check_dict[check_name].setFont(0,font)
                    self.tree_widget.addTopLevelItem(check_dict[check_name])

                resource_id = recommendation['Category']
                status = recommendation['Status']
                metadata = recommendation.get('Metadata', 'N/A')
                # Create the item and set text alignment
                '''
                print(check_name)
                print(resource_id)
                print(status)
                print(metadata)
                print(recommendation['Recommended Action'])
                print("****************************")
                '''
                resource_item = QTreeWidgetItem([check_name, resource_id, status, metadata, recommendation['Recommended Action']])
                #resource_item = QTreeWidgetItem(str(check_name), str(resource_id), str(status), str(metadata), str(recommendation['Recommended Action']))
                for i in range(5):
                    resource_item.setTextAlignment(i, Qt.AlignLeft | Qt.AlignTop)
                    resource_item.setFont(i,font)

                check_dict[check_name].addChild(resource_item)


            #self.table_widget.resizeColumnsToContents()
            self.layout = QVBoxLayout(self.tree_widget)#table_widget)
            self.formGroupBox.setFixedWidth(1500)
            self.formGroupBox.setFixedHeight(500)
            self.formGroupBox.setLayout(self.layout)
            self.table_widget.verticalHeader().setVisible(False)
            self.formGroupBox.layout().addWidget(self.tree_widget)#table_widget)
            self.formGroupBox.show()
            #self.setCentralWidget.show()
            return recommendations

        except NoCredentialsError:
            print("Error: No AWS credentials found. Please configure your credentials.")
            return None
        except PartialCredentialsError:
            print("Error: Incomplete AWS credentials found. Please check your configuration.")
            return None
        except Exception as e:
            print(f"An error occurred: {e}")
            return None

    #def check_deployment_status(self):


    def find_item(self):
        widget5.clear() #f =  open("awsservices.txt", "r")
        #listall = f.read().splitlines()
        for item in awslist[1:]:
            splitlist = item.split('^')
            if self.le_search.text().lower() in item.lower():
                widget5.addItem(splitlist[0])


    def updatePlaceholder(self):
        red_text = combobox1.currentText()
        self.flag = True
        if t1.toPlainText().startswith("Enter "):
            t1.setHtml(f" Enter business requirements here for <span style=\"color:#ff0000;\" > {red_text} </span> domain")
        else:
            print("ELSE") #t1.setFocus()

    def read_pdf(self, file_path):
        # Reads text content from a PDF file.
        text = ""
        with open(file_path, "rb") as file:
            reader = PyPDF2.PdfReader(file)
            for page_num in range(len(reader.pages)):
                page = reader.pages[page_num]
                text += page.extract_text()
        return text

    def chunk_text(self, text, chunk_size=512):
        # Chunks text into smaller segments.
        chunks = []
        for i in range(0, len(text), chunk_size):
            chunk = text[i:i+chunk_size]
            chunks.append(chunk)
        return chunks

    def process_pdf(self, file_path):
        # Reads PDF, performs chunking, creates embeddings, and stores them.
        text = self.read_pdf(file_path)
        chunks = self.chunk_text(text)
        embeddings = self.create_embeddings(chunks)
        a = {}
        for i in range(len(embeddings.data)):
            a[str(i)]=chunks[i]
            vector = [{'id':str(i),'values':embeddings.data[0].embedding}]
        print("insert successfully")
        return a

    def llamaPrompt(self):
        print("IN LLAMA")
        #from gradio_client import Client
        prompt = t1.toPlainText()
        client = Client("https://ed0d6d073d592c8416.gradio.live/")
        #client = Client("https://0.0.0.0:1111")
        result = client.predict(
                "",        # str  in 'Input the Instruction (System Prompt)' Textbox component
                prompt,      # str  in 'Input the Data (User Prompt)' Textbox component
                api_name="/predict"
        ) #print(result)
        self.t2.setText(result)
    '''
    def create_embeddings(chunk):
        #client = AzureOpenAI(api_key = os.getenv("AZURE_OPENAI_KEY"),api_version = "2023-05-15",azure_endpoint =os.getenv("AZURE_OPENAI_ENDPOINT"))
        client = AzureOpenAI(api_key ="********************************",api_version ="2023-07-01-preview",azure_endpoint ="https://chetan-gpt35.openai.azure.com/")
        response = client.embeddings.create(input = chunk ,model= "text-embedding-chetan")
        #print(response.model_dump_json(indent=2))
        return response
    '''
    def create_embeddings(self, chunk):
        client = AzureOpenAI(api_key ="********************************",api_version ="2023-07-01-preview",azure_endpoint ="https://chetan-gpt35.openai.azure.com/")
        response = client.embeddings.create(input = chunk ,model= "text-embedding-chetan")
        return response

    def topk_retrieval(self, q_embeddings,a):
        pc = Pinecone(api_key="930b4a18-ceae-4fd0-848d-aad4a52e9dbf",environment='gcp-starter')
        index = pc.Index('index384')
        print(index.describe_index_stats())
        s= index.query(vector=[q_embeddings], top_k=5000,include_metadata=True)
        #print(s,len(s['matches']))
        context = []
        for i in range(len(s['matches'])):
            x = s['matches'][i].id
            context.append(a[x])
        #print(a[x])
        return context

    def process_zip(self, extract_dir):
        # Extract the ZIP file
        #with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        #    zip_ref.extractall(extract_dir)
        all_chunks = []
        # Iterate through the extracted files
        for root, dirs, files in os.walk(extract_dir):
            for file in files:
                if file.endswith(".pdf"):
                    doc_path = os.path.join(root, file)

                    # Load your PDF doc
                    loader = PyPDFLoader(doc_path)
                    pages = loader.load()
                    '''
                    pages=""
                    with open(doc_path, "rb") as thisfile:
                        reader = PyPDF2.PdfReader(thisfile)
                        for page_num in range(len(reader.pages)):
                            page = reader.pages[page_num]
                            pages += page.extract_text()

                    '''
                    #Split the doc into smaller chunks
                    text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=50)
                    chunks = text_splitter.split_documents(pages)
                    all_chunks.extend(chunks)
        return all_chunks



    def find_best_match(self,target, options):
        # Get the closest matches; n=1 returns the single best match
        matches = difflib.get_close_matches(target, options, n=1, cutoff=0.0)
        return matches[0] if matches else None

    def gptPrompt(self):
        self.formGroupBox.close()
        print("processing")

        # Create a custom dialog with a progress bar instead of a simple message box
        progressDialog = QDialog()
        progressDialog.setWindowTitle("Processing Submit Written Requirements")
        progressDialog.setWindowIcon(QtGui.QIcon('mapleicon.png'))
        progressDialog.setFixedSize(400, 100)

        # Create layout for the dialog
        layout = QVBoxLayout(progressDialog)

        # Add a label to show processing status
        statusLabel = QLabel("Processing your requirements...")
        layout.addWidget(statusLabel)

        # Create and add progress bar
        progressBar = QProgressBar(progressDialog)
        progressBar.setRange(0, 100)
        progressBar.setValue(0)
        progressBar.setTextVisible(True)
        progressBar.setFormat("%p%")
        layout.addWidget(progressBar)

        # Show the dialog but don't block (non-modal)
        progressDialog.show()
        QApplication.processEvents()  # Process events to ensure UI updates
        # Update progress to 10%
        progressBar.setValue(10)
        statusLabel.setText("Initializing...")
        QApplication.processEvents()

        if self.system_prompt is None:
            for i in range(self.layoutIn1.count()):
                item = self.layoutIn1.itemAt(i)
                print(item.widget())
                if item and isinstance(item.widget(),QTextEdit):
                    self.system_prompt = str(item.widget().document().toPlainText())
            self.formGroupBox1.close()

        # Update progress to 20%
        progressBar.setValue(20)
        statusLabel.setText("Processing requirements...")
        QApplication.processEvents()

        self.defaultquery=self.system_prompt
        #q_emd = self.create_embeddings(system_prompt)
        #dict1 = self.process_pdf("lib/aws-cloud-design-patterns.pdf")
        #context_LLM = self.topk_retrieval(q_emd.data[0].embedding,dict1)
        #context_to_prompt = f"{context_LLM}\n"

        # Update progress to 30%
        progressBar.setValue(30)
        statusLabel.setText("Loading embeddings...")
        QApplication.processEvents()

        #all_chunks = self.process_zip("docs")
        embeddings = SentenceTransformerEmbeddings(model_name="all-MiniLM-L6-v2")
        #db_chroma = Chroma.from_documents(all_chunks, embeddings, persist_directory="noah_db")
        #db_chroma.persist()
        #context_text = ""

        # Update progress to 40%
        progressBar.setValue(40)
        statusLabel.setText("Searching for relevant information...")
        QApplication.processEvents()

        db_chroma = Chroma(persist_directory="noah_db1", embedding_function=embeddings)
        #self.system_prompt="Here is a description of an application for which we need to design an AWS cloud-based architecture. A live camera continuously captures videos. This is collected by an edge-device for processing. The data which arrives in the form of streams in then processed and passed to a vision tracking deep learning model. The model uses this input data for downstream tasks such as inference. The results of inference are stored, further processed and shown to the user in his browser for analysis.  This was the application description. Design an AWS cloud-based architecture. Identify all the necessary components of the architecture. Provide me with a sequential list of the components based on the flow of the application from input to output. Also list the AWS cloud services separately."
        print(self.system_prompt)

        # Update progress to 50%
        progressBar.setValue(50)
        statusLabel.setText("Performing similarity search...")
        QApplication.processEvents()

        docs_chroma = db_chroma.similarity_search_with_score(self.system_prompt, k=5)

        # Update progress to 60%
        progressBar.setValue(60)
        statusLabel.setText("Processing context information...")
        QApplication.processEvents()

        context_text = "\n\n".join([doc.page_content for doc, _score in docs_chroma])
        #print(awslist)
        newawslist=awslist.copy()
        if "Services" in newawslist:
            newawslist.remove("Services")
        # Update progress to 70%
        progressBar.setValue(70)
        statusLabel.setText("Preparing query with your answers...")
        QApplication.processEvents()

        qa=""
        ans=0
        #if len(self.answers)>0:
        for q in self.answers:
            qa+=self.answers[ans]["question"]+"\n"+self.answers[ans]["answer"]+"\n\n"
            ans+=1
        completions_deployment = "deployed-chetan"
        client = AzureOpenAI(api_key=api_key, api_version=api_version, azure_endpoint = api_base)

        # Update progress to 75%
        progressBar.setValue(75)
        statusLabel.setText("Generating architecture recommendations...")
        QApplication.processEvents()

        if len(qa)>0:
            messages=[ {"role": "user", "content": context_text},{"role": "user", "content": self.system_prompt+"\nFor the above query reply with just a list of the required services. Do not write any more text. Do not provide any explanation. Do not write any prefix sentences. Do not provide numbering of the services.Just write the service names one after the other seperated by newline only, not comma etc. I have also a list of questions and answers, that will provide you extra information about the query. They are: "+qa+"The services you predict, should be always from the valid AWS services list given below. Do not predict a service that is not in the valid AWS services list. And the text you predict should be ditto from the valid AWS services list. Do not change a word, or do not shorten the text of any service. The list is:\n "+"\n".join(newawslist)}]#,{"role": "user", "content": self.conversation_history} ]
        else:
            messages=[ {"role": "user", "content": context_text},{"role": "user", "content": self.system_prompt+"\nFor the above query reply with just a list of the required services. Do not write any more text. Do not provide any explanation. Do not write any prefix sentences. Do not provide numbering of the services.Just write the service names one after the other seperated by newline only, not comma etc. The services you predict, should be always from the valid AWS services list given below. Do not predict a service that is not in the valid AWS services list. And the text you predict should be ditto from the valid AWS services list. Do not change a word, or do not shorten the text of any service. The list is:\n "+"\n".join(newawslist)}]

        # Update progress to 80%
        progressBar.setValue(80)
        statusLabel.setText("Receiving AI response...")
        QApplication.processEvents()

        #messages=[ {"role": "user", "content": system_prompt} ]
        #self.conversation_history.append({"role": "user", "content": messages}) #Chat
        response = client.chat.completions.create(messages=messages,model=deployment_name)
        retval=response.choices[0].message.content.strip().replace("\n\n","\n").split("\n")#.append("User")
        retval.append("User")
        #print(awslist)
        print("retvalllllllllllllllllllllllllllllllllllllllllllllllll")
        print(retval)
        responsenew=[]
        print(retval)

        # Update progress to 85%
        progressBar.setValue(85)
        statusLabel.setText("Processing service connections...")
        QApplication.processEvents()

        for resp in retval:
            if resp!="User" or resp not in newawslist:
                print(resp)
                best_match = self.find_best_match(resp, newawslist)
                responsenew.append(best_match)
            else:
                responsenew.append(resp)
        #self.conversation_history.append({"role": "assistant", "content": response.choices[0].message.content})

        messages=[{"role":"user","content":context_text},{"role": "user", "content":self.system_prompt+"\nFor the above query the cloud services required to design the application, are as below:\n"+str(responsenew)+"\n Now suggest suitable connections between these cloud services which would indicate the data flow between them. The connections should include many to one and one to many connections. But be careful that a single instance of a cloud service, do not have more than 10 target services. It is absolute must that the starting point of the graph is always and always the 'User'. The resulting network should serve the purpose of the application in the query above. Do not write any explanations or descriptions. Just list the connections as pairs of services. Do not write any extra text.Let each pair be seperated by a '-'. Thus a service should never ever be in more than 10 pairs. Do not provide any numbering of the services. Just write each pair in a new line."}]

        # Update progress to 90%
        progressBar.setValue(90)
        statusLabel.setText("Generating network connections...")
        QApplication.processEvents()

        network=client.chat.completions.create(messages=messages,model=deployment_name).choices[0].message.content.strip().replace("\n\n","\n")
        #print(client.chat.completions.create(messages=messages,model=deployment_name))
        network=network.split("\n")
        print(network)

        # Update progress to 95%
        progressBar.setValue(95)
        statusLabel.setText("Processing network connections...")
        QApplication.processEvents()

        newnetwork=[]
        for resp in network:
            if " - " not in resp:
                continue
            respnow1=resp.split(" - ")[0]
            if respnow1!="User" or respnow1 not in newawslist:
                #print(respnow1)
                best_match = self.find_best_match(respnow1, newawslist)
                respnow1=best_match
            else:
                respnow1=respnow1
            respnow2=resp.split(" - ")[1]
            if respnow2!="User" or respnow2 not in newawslist:
                #print(respnow1)
                best_match = self.find_best_match(respnow2, newawslist)
                respnow2=best_match
            else:
                respnow2=respnow2
            newnetwork.append(respnow1+" - "+respnow2)
        print("newnetwork..............")
        print(newnetwork)

        # Update progress to 100%
        progressBar.setValue(100)
        statusLabel.setText("Completed! Generating visualization...")
        QApplication.processEvents()

        # Close the progress dialog
        progressDialog.close()

        # Plot the graph with the results
        self.plotGraph("\n".join(newnetwork))

    def uploadprompt(self):
        self.upload=True
        options = QFileDialog.Options()
        options |= QFileDialog.DontUseNativeDialog
        fileName, _ = QFileDialog.getOpenFileName(
            self,
            "RFP Load",
            ".",
            "All Supported Files (*.pdf *.txt);;PDF Files (*.pdf);;Text Files (*.txt)",
            options=options
        )

        if not fileName:  # User canceled the dialog
            return

        # Show a loading dialog
        loading_dialog = QDialog(self)
        loading_dialog.setWindowTitle("Loading Document")
        loading_dialog.setFixedSize(300, 100)
        loading_layout = QVBoxLayout(loading_dialog)
        loading_label = QLabel("Reading document content...")
        loading_layout.addWidget(loading_label)
        loading_progress = QProgressBar()
        loading_progress.setRange(0, 0)  # Indeterminate progress
        loading_layout.addWidget(loading_progress)
        loading_dialog.show()
        QApplication.processEvents()

        self.system_prompt=""
        if fileName.endswith(".txt"):
            with open(fileName,encoding="utf-8",errors="replace") as file:
                self.system_prompt=file.read().strip()
        else:
            pdfFileObj = open(fileName, 'rb')
            pdfReader = PyPDF2.PdfReader(pdfFileObj)
            self.system_prompt=""
            doc=[]
            for i in range(len(pdfReader.pages)):
                pageObj = pdfReader.pages[i]
                doc.extend(pageObj.extract_text().split("\n"))
            for d in doc:
                self.system_prompt+=d+" "
            self.system_prompt=self.system_prompt.strip()

        loading_label.setText("Analyzing document content...")
        QApplication.processEvents()

        # Analyze the document to identify missing information
        self.analyzeDocumentContent(loading_dialog)

    def analyzeDocumentContent(self, loading_dialog=None):
        """
        Analyze the uploaded document content using ChatGPT API to identify missing information
        that would be beneficial for cloud architecture design.
        """
        try:
            # Create a progress dialog if one wasn't provided
            if not loading_dialog:
                loading_dialog = QDialog(self)
                loading_dialog.setWindowTitle("Analyzing Document")
                loading_dialog.setFixedSize(300, 100)
                loading_layout = QVBoxLayout(loading_dialog)
                loading_label = QLabel("Analyzing document content...")
                loading_layout.addWidget(loading_label)
                loading_progress = QProgressBar()
                loading_progress.setRange(0, 0)  # Indeterminate progress
                loading_layout.addWidget(loading_progress)
                loading_dialog.show()
                QApplication.processEvents()

            # Prepare the prompt for ChatGPT
            analysis_prompt = f"""
            I have a document with requirements for a cloud architecture design. Here's the content:

            {self.system_prompt}

            Based on this document, I need to identify what important information is missing that would help design a better cloud architecture.

            Please analyze the document and identify what key information is missing or would be beneficial to have in the following categories:
            1. Usage Patterns
            2. Data Storage and Retrieval
            3. Compute Requirements
            4. Networking
            5. Scalability and Elasticity
            6. Security
            7. Integration and Interoperability
            8. Logging
            9. Cost Considerations
            10. User Experience
            11. Data Transfer and Bandwidth

            For each category that needs more information, create 1-2 interactive questions. Make these questions conversational and engaging.

            For each question, include:
            - The question text (conversational, as if you're directly talking to the user)
            - The question type (one of: "open", "mcq", "yes_no")
            - For MCQ questions, include an array of possible options
            - A validation hint that explains what kind of answer you're looking for
            - A follow-up response for valid answers
            - A clarification response for unclear or invalid answers

            Format your response as a JSON object with categories as keys and arrays of question objects as values.
            Example:
            {{
                "Usage Patterns": [
                    {{
                        "question": "I'm curious about your expected user base. Roughly how many concurrent users do you anticipate during peak times?",
                        "type": "open",
                        "validation_hint": "Looking for a number or range of users",
                        "follow_up": "That's helpful! Understanding your user scale will help us determine the right sizing for your services.",
                        "clarification": "I'm looking for an estimate of how many users might be using the system at the same time. Even a rough range would be helpful."
                    }},
                    {{
                        "question": "Do you expect your traffic to be consistent throughout the day, or will there be specific peak hours?",
                        "type": "mcq",
                        "options": ["Consistent traffic", "Specific peak hours", "Unpredictable spikes", "Seasonal variations"],
                        "validation_hint": "Select the option that best describes your traffic pattern",
                        "follow_up": "Thanks! This will help us design for the right scaling approach.",
                        "clarification": "Please select one of the options that best matches your expected traffic pattern."
                    }}
                ],
                "Security": [
                    {{
                        "question": "Does your application need to comply with any specific regulatory requirements like GDPR, HIPAA, or PCI DSS?",
                        "type": "yes_no",
                        "validation_hint": "A yes or no answer, with details if yes",
                        "follow_up": "Thanks for clarifying. This will influence our security architecture recommendations.",
                        "clarification": "I'm asking if there are any legal or industry regulations that your system needs to follow regarding data security and privacy."
                    }}
                ]
            }}

            Only include categories that need questions. If a category is well covered, exclude it from the JSON.
            """

            # Call ChatGPT API
            client = AzureOpenAI(api_key=api_key, api_version=api_version, azure_endpoint=api_base)

            # Update the loading dialog
            if hasattr(loading_dialog, 'findChild'):
                label = loading_dialog.findChild(QLabel)
                if label:
                    label.setText("Consulting AI to identify missing information...")
                    QApplication.processEvents()

            # Make the API call with retry mechanism
            max_retries = 3
            retry_count = 0
            retry_delay = 2  # Initial delay in seconds
            success = False

            while retry_count < max_retries and not success:
                try:
                    response = client.chat.completions.create(
                        messages=[{"role": "user", "content": analysis_prompt}],
                        model=deployment_name
                    )
                    analysis_result = response.choices[0].message.content.strip()
                    success = True

                except Exception as e:
                    retry_count += 1
                    print(f"API call failed (attempt {retry_count}/{max_retries}): {str(e)}")

                    if retry_count < max_retries:
                        # Update status to show waiting for retry
                        if hasattr(loading_dialog, 'findChild'):
                            label = loading_dialog.findChild(QLabel)
                            if label:
                                label.setText(f"Connection issue. Retrying in {retry_delay} seconds...")
                                QApplication.processEvents()

                        # Wait before retrying with exponential backoff
                        time.sleep(retry_delay)
                        retry_delay *= 2  # Exponential backoff
                    else:
                        # Show error message dialog
                        loading_dialog.close()
                        error_dialog = QMessageBox()
                        error_dialog.setIcon(QMessageBox.Critical)
                        error_dialog.setWindowTitle("Connection Error")
                        error_dialog.setText("Failed to analyze document with AI")
                        error_dialog.setInformativeText(f"Error details: {str(e)}\n\nFalling back to standard questions.")
                        error_dialog.setStandardButtons(QMessageBox.Ok)
                        error_dialog.exec_()

                        # Fall back to standard questions
                        self.displayQuestions()
                        return

            # Close the loading dialog
            loading_dialog.close()

            # Parse the JSON response
            try:
                import json
                import re

                # Try to extract JSON from the response using regex in case there's additional text
                json_match = re.search(r'({[\s\S]*})', analysis_result)
                if json_match:
                    analysis_result = json_match.group(1)

                missing_info = json.loads(analysis_result)

                # If we got valid questions, display the dynamic chatbot interface
                if missing_info and isinstance(missing_info, dict) and len(missing_info) > 0:
                    self.displayDynamicQuestions(missing_info)
                else:
                    # If no questions were identified, proceed directly to architecture generation
                    self.answers = []
                    self.gptPrompt()
            except json.JSONDecodeError as e:
                print(f"Error parsing JSON response: {e}")
                print(f"Response was: {analysis_result}")
                # Fall back to standard questions
                self.displayQuestions()

        except Exception as e:
            print(f"Error in analyzeDocumentContent: {e}")
            # Fall back to standard questions
            if loading_dialog and hasattr(loading_dialog, 'close'):
                loading_dialog.close()
            self.displayQuestions()

    def displayDynamicQuestions(self, missing_info):
        """
        Display a chatbot-like interface to ask only the questions that are relevant
        based on the AI analysis of the document.

        Args:
            missing_info (dict): Dictionary with categories as keys and lists of questions as values
        """
        self.answers = []
        self.current_question_index = 0
        self.dynamic_questions = []
        self.mcq_widgets = {}  # Store MCQ widgets for each question

        # Flatten the questions from the categories into a single list
        for category, questions in missing_info.items():
            for question_obj in questions:
                # Add category to the question object
                question_obj["category"] = category
                question_obj["answer"] = ""
                question_obj["answered"] = False
                self.dynamic_questions.append(question_obj)

        # If no questions, proceed directly to architecture generation
        if len(self.dynamic_questions) == 0:
            self.gptPrompt()
            return

        # Create the chatbot-like interface
        self.formGroupBox = QGroupBox()
        self.formGroupBox.setWindowTitle("AI Assistant")
        self.formGroupBox.setStyleSheet("""
            QGroupBox {
                background-color: #FFFFFF;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            QGroupBox::title {
                color: white;
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 5px 15px;
                background-color: #FF8C00;
                border-radius: 4px;
            }
        """)

        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(15, 20, 15, 15)
        main_layout.setSpacing(12)

        # Add introduction text
        intro_label = QLabel("Hi there! I've analyzed your document and would like to ask a few questions to help design the best cloud architecture for your needs. Feel free to ask me to clarify anything that's not clear.")
        intro_label.setWordWrap(True)
        intro_label.setStyleSheet("""
            font-weight: bold;
            color: #333333;
            margin-bottom: 10px;
            font-size: 14px;
            background-color: #F5F5F5;
            padding: 10px;
            border-radius: 5px;
            border-left: 3px solid #FF8C00;
        """)
        main_layout.addWidget(intro_label)

        # Create chat history area
        self.chat_history = QTextEdit()
        self.chat_history.setReadOnly(True)
        self.chat_history.setStyleSheet("""
            QTextEdit {
                background-color: #FFFFFF;
                color: #333333;
                border: 1px solid #E0E0E0;
                border-radius: 5px;
                padding: 10px;
                font-size: 13px;
                line-height: 1.5;
            }
            QScrollBar:vertical {
                background-color: #F5F5F5;
                width: 12px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #FF8C00;
                min-height: 20px;
                border-radius: 6px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)
        main_layout.addWidget(self.chat_history)

        # Create MCQ options area (will be shown/hidden as needed)
        self.mcq_area = QWidget()
        self.mcq_layout = QVBoxLayout(self.mcq_area)
        self.mcq_area.setVisible(False)
        self.mcq_area.setStyleSheet("""
            QWidget {
                background-color: #F8F8F8;
                border: 1px solid #E0E0E0;
                border-radius: 5px;
                padding: 10px;
                margin-top: 5px;
            }
            QRadioButton {
                padding: 8px;
                font-size: 13px;
                color: #333333;
                spacing: 8px;
            }
            QRadioButton:hover {
                background-color: #F0F0F0;
                border-radius: 3px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
            QRadioButton::indicator:checked {
                background-color: #FF8C00;
                border: 2px solid #FFFFFF;
                border-radius: 8px;
            }
        """)
        main_layout.addWidget(self.mcq_area)

        # Create yes/no buttons area (will be shown/hidden as needed)
        self.yes_no_area = QWidget()
        self.yes_no_layout = QHBoxLayout(self.yes_no_area)
        self.yes_no_area.setStyleSheet("""
            QWidget {
                background-color: #F8F8F8;
                border: 1px solid #E0E0E0;
                border-radius: 5px;
                padding: 10px;
            }
        """)

        self.yes_button = QPushButton("Yes")
        self.yes_button.setStyleSheet("""
            QPushButton {
                background-color: #FF8C00;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-size: 13px;
                min-width: 80px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #FFA500;
            }
        """)
        self.yes_button.clicked.connect(lambda: self.handle_yes_no_answer("Yes"))

        self.no_button = QPushButton("No")
        self.no_button.setStyleSheet("""
            QPushButton {
                background-color: #E0E0E0;
                color: #333333;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-size: 13px;
                min-width: 80px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #D0D0D0;
            }
        """)
        self.no_button.clicked.connect(lambda: self.handle_yes_no_answer("No"))

        self.yes_no_layout.addWidget(self.yes_button)
        self.yes_no_layout.addWidget(self.no_button)
        self.yes_no_layout.addStretch()

        self.yes_no_area.setVisible(False)
        main_layout.addWidget(self.yes_no_area)

        # Create input area for open questions
        self.input_area = QWidget()
        self.input_area.setStyleSheet("""
            QWidget {
                background-color: #F8F8F8;
                border: 1px solid #E0E0E0;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        input_layout = QHBoxLayout(self.input_area)

        self.answer_input = QTextEdit()
        self.answer_input.setFixedHeight(60)
        self.answer_input.setPlaceholderText("Type your answer here...")
        self.answer_input.setStyleSheet("""
            QTextEdit {
                background-color: #FFFFFF;
                color: #333333;
                border: 1px solid #E0E0E0;
                border-radius: 5px;
                padding: 8px;
                font-size: 13px;
            }
            QTextEdit:focus {
                border: 1px solid #FF8C00;
            }
        """)
        # Connect Enter key to submit
        self.answer_input.installEventFilter(self)

        input_layout.addWidget(self.answer_input)

        button_layout = QVBoxLayout()
        button_layout.setSpacing(8)

        self.submit_answer_button = QPushButton("Submit")
        self.submit_answer_button.setStyleSheet("""
            QPushButton {
                background-color: #FF8C00;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 5px 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #FFA500;
            }
        """)
        self.submit_answer_button.clicked.connect(self.submit_dynamic_answer)
        button_layout.addWidget(self.submit_answer_button)

        self.skip_question_button = QPushButton("Skip")
        self.skip_question_button.setStyleSheet("""
            QPushButton {
                background-color: #E0E0E0;
                color: #333333;
                border: none;
                border-radius: 4px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #D0D0D0;
            }
        """)
        self.skip_question_button.clicked.connect(self.skip_dynamic_question)
        button_layout.addWidget(self.skip_question_button)

        input_layout.addLayout(button_layout)
        main_layout.addWidget(self.input_area)

        # Add finish button at the bottom
        self.finish_button = QPushButton("Finish and Generate Architecture")
        self.finish_button.setStyleSheet("""
            QPushButton {
                background-color: #FF8C00;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 15px;
                margin-top: 10px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #FFA500;
            }
        """)
        self.finish_button.clicked.connect(self.finish_dynamic_questions)
        main_layout.addWidget(self.finish_button)

        # Set up the form
        self.formGroupBox.setLayout(main_layout)
        self.formGroupBox.setFixedWidth(700)
        self.formGroupBox.setFixedHeight(600)
        self.formGroupBox.show()

        # Add a welcome message
        self.chat_history.append(f"""<div style='color: #333333; background-color: #FFFFFF; padding: 10px; border-radius: 5px; margin: 5px 0; border-left: 3px solid #FF8C00; box-shadow: 0 1px 2px rgba(0,0,0,0.1);'><b>AI:</b>
            Welcome! I'll be asking you a few targeted questions to help design the optimal cloud architecture for your needs.
            Let's start with the first question:
        </div>""")

        # Display the first question
        self.display_next_dynamic_question()

    def eventFilter(self, obj, event):
        """Handle Enter key press in the text input"""
        if obj == self.answer_input and event.type() == QEvent.KeyPress:
            if event.key() == Qt.Key_Return and event.modifiers() != Qt.ShiftModifier:
                self.submit_dynamic_answer()
                return True
        return super().eventFilter(obj, event)

    def display_next_dynamic_question(self):
        """Display the next question in the chatbot interface"""
        # Hide all input areas first
        self.input_area.setVisible(False)
        self.mcq_area.setVisible(False)
        self.yes_no_area.setVisible(False)

        # Clear any existing MCQ widgets
        for i in reversed(range(self.mcq_layout.count())):
            widget = self.mcq_layout.itemAt(i).widget()
            if widget:
                widget.deleteLater()

        if self.current_question_index < len(self.dynamic_questions):
            question_data = self.dynamic_questions[self.current_question_index]
            category = question_data["category"]
            question_text = question_data["question"]
            question_type = question_data.get("type", "open")

            # Format the question with category
            formatted_question = f"<b>[{category}]</b> {question_text}"

            # Add the question to the chat history
            self.chat_history.append(f"<div style='color: #333333; background-color: #FFFFFF; padding: 8px; border-radius: 5px; margin: 5px 0; border-left: 3px solid #FF8C00; box-shadow: 0 1px 2px rgba(0,0,0,0.1);'><b>AI:</b> {formatted_question}</div>")

            # Show the appropriate input method based on question type
            if question_type == "mcq":
                # Create MCQ options
                options = question_data.get("options", [])
                if options:
                    self.mcq_area.setVisible(True)
                    self.mcq_group = QButtonGroup(self)

                    for i, option in enumerate(options):
                        radio = QRadioButton(option)
                        radio.setStyleSheet("margin: 5px;")
                        self.mcq_layout.addWidget(radio)
                        self.mcq_group.addButton(radio, i)

                    # Add a submit button for MCQ
                    mcq_submit = QPushButton("Submit Selection")
                    mcq_submit.setStyleSheet("""
                        QPushButton {
                            background-color: #4CAF50;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            padding: 5px 10px;
                            margin-top: 10px;
                        }
                        QPushButton:hover {
                            background-color: #45a049;
                        }
                    """)
                    mcq_submit.clicked.connect(self.submit_mcq_answer)
                    self.mcq_layout.addWidget(mcq_submit)
                else:
                    # Fallback to open question if no options provided
                    self.input_area.setVisible(True)
            elif question_type == "yes_no":
                # Show yes/no buttons
                self.yes_no_area.setVisible(True)
            else:
                # Default to open question
                self.input_area.setVisible(True)

            # Add validation hint if available
            if "validation_hint" in question_data:
                hint = question_data["validation_hint"]
                self.chat_history.append(f"<div style='color: #777777; font-style: italic; margin-left: 20px; margin-top: 5px; margin-bottom: 8px;'><small>{hint}</small></div>")

            # Scroll to the bottom
            self.chat_history.verticalScrollBar().setValue(
                self.chat_history.verticalScrollBar().maximum()
            )

            # Clear the input field and set focus for open questions
            if question_type == "open":
                self.answer_input.clear()
                self.answer_input.setFocus()
        else:
            # No more questions, proceed to architecture generation
            self.finish_dynamic_questions()

    def submit_mcq_answer(self):
        """Handle submission of MCQ answers"""
        if self.current_question_index < len(self.dynamic_questions):
            selected_button = self.mcq_group.checkedButton()

            if selected_button:
                answer = selected_button.text()
                self.process_answer(answer)
            else:
                # No option selected
                self.chat_history.append(f"<div style='color: #333333; background-color: #FFFFFF; padding: 8px; border-radius: 5px; margin: 5px 0; border-left: 3px solid #FF5722; box-shadow: 0 1px 2px rgba(0,0,0,0.1);'><b>AI:</b> Please select one of the options before submitting.</div>")
                self.chat_history.verticalScrollBar().setValue(
                    self.chat_history.verticalScrollBar().maximum()
                )

    def handle_yes_no_answer(self, answer):
        """Handle Yes/No button clicks"""
        if self.current_question_index < len(self.dynamic_questions):
            self.process_answer(answer)

    def process_answer(self, answer):
        """Process any type of answer and determine if it's valid using OpenAI API"""
        if self.current_question_index < len(self.dynamic_questions):
            question_data = self.dynamic_questions[self.current_question_index]

            # Add the answer to the chat history
            if answer:
                self.chat_history.append(f"<div style='color: #333333; background-color: #F0F0F0; padding: 8px; border-radius: 5px; margin: 5px 0; border-left: 3px solid #FF8C00;'><b>You:</b> {answer}</div>")
            else:
                self.chat_history.append(f"<div style='color: #333333; background-color: #F0F0F0; padding: 8px; border-radius: 5px; margin: 5px 0; border-left: 3px solid #FF8C00;'><b>You:</b> <i>(No answer provided)</i></div>")

            # Show thinking indicator
            thinking_id = self.show_ai_thinking()

            # Get question details
            question_type = question_data.get("type", "open")
            question_text = question_data.get("question", "")

            # Use OpenAI to analyze the answer
            is_valid, ai_response = self.analyze_answer_with_ai(question_data, answer)

            # Remove thinking indicator
            self.remove_ai_thinking(thinking_id)

            # Store the answer regardless of validity
            question_data["answer"] = answer
            question_data["answered"] = True

            # Display AI response
            self.chat_history.append(f"<div style='color: #333333; background-color: #FFFFFF; padding: 8px; border-radius: 5px; margin: 5px 0; border-left: 3px solid #FF8C00; box-shadow: 0 1px 2px rgba(0,0,0,0.1);'><b>AI:</b> {ai_response}</div>")

            # Proceed based on validity
            if is_valid:
                # Move to the next question
                self.current_question_index += 1

                # Display the next question or finish
                if self.current_question_index < len(self.dynamic_questions):
                    # Add a small delay to make it feel more natural
                    QTimer.singleShot(800, self.display_next_dynamic_question)
                else:
                    self.finish_dynamic_questions()
            else:
                # Keep the same question active
                if question_type == "open":
                    self.answer_input.clear()
                    self.answer_input.setFocus()

            # Scroll to the bottom
            self.chat_history.verticalScrollBar().setValue(
                self.chat_history.verticalScrollBar().maximum()
            )

    def show_ai_thinking(self):
        """Show an indicator that the AI is thinking"""
        thinking_id = f"thinking_{time.time()}"
        self.chat_history.append(f"<div id='{thinking_id}' style='color: #888888; font-style: italic; margin: 5px 0; text-align: center;'><i>AI is thinking...</i></div>")
        self.chat_history.verticalScrollBar().setValue(
            self.chat_history.verticalScrollBar().maximum()
        )
        QApplication.processEvents()  # Force UI update
        return thinking_id

    def remove_ai_thinking(self, thinking_id):
        """Remove the thinking indicator"""
        # This is a bit of a hack since QTextEdit doesn't easily support removing elements
        # We'll use JavaScript to find and remove the element if we were using a web view
        # For now, we'll just append a new message and rely on scrolling
        pass

    def analyze_answer_with_ai(self, question_data, answer):
        """
        Use OpenAI API to analyze the user's answer and determine if it's valid.
        Returns a tuple of (is_valid, ai_response)
        """
        try:
            # Basic validation for empty answers
            if not answer or not answer.strip():
                return False, "I need some information to proceed. Could you please provide an answer?"

            # Get question details
            question_type = question_data.get("type", "open")
            question_text = question_data.get("question", "")
            category = question_data.get("category", "")

            # For MCQ and Yes/No questions, we can do simple validation without API
            if question_type == "mcq":
                options = question_data.get("options", [])
                if options and answer not in options:
                    return False, f"Please select one of the provided options: {', '.join(options)}"
                return True, question_data.get("follow_up", "Thanks for your answer!")

            elif question_type == "yes_no":
                lower_answer = answer.lower()
                if lower_answer not in ["yes", "no", "yeah", "nope", "correct", "incorrect", "definitely", "absolutely"]:
                    return False, "I was expecting a 'Yes' or 'No' answer. Could you please clarify?"
                return True, question_data.get("follow_up", "Thanks for your answer!")

            # For open-ended questions, use OpenAI API for analysis
            # Prepare the prompt for ChatGPT
            analysis_prompt = f"""
            I'm a cloud architecture assistant asking questions to gather requirements. I just asked this question:

            Category: {category}
            Question: "{question_text}"

            The user responded with: "{answer}"

            Please analyze if this answer is valid and relevant to my question. Consider:
            1. Is the answer on-topic and relevant to what I asked?
            2. Does it provide the specific information I was looking for?
            3. Is it a complete answer or does it need clarification?

            If the answer is valid and helpful, respond with:
            VALID: [A natural, conversational acknowledgment of their answer that feels warm and human-like]

            If the answer is invalid, unclear, or off-topic, respond with:
            INVALID: [A polite request for clarification that explains what specific information I need]

            Your response should be conversational and friendly, as if you're having a natural chat. Don't be robotic.
            """

            # Call OpenAI API
            client = AzureOpenAI(api_key=api_key, api_version=api_version, azure_endpoint=api_base)

            # Make the API call with retry mechanism
            max_retries = 2
            retry_count = 0
            retry_delay = 1  # Initial delay in seconds

            while retry_count < max_retries:
                try:
                    response = client.chat.completions.create(
                        messages=[{"role": "user", "content": analysis_prompt}],
                        model=deployment_name,
                        temperature=0.7  # Add some variability to responses
                    )

                    ai_response = response.choices[0].message.content.strip()

                    # Parse the response to determine validity
                    if ai_response.startswith("VALID:"):
                        return True, ai_response[6:].strip()
                    elif ai_response.startswith("INVALID:"):
                        return False, ai_response[8:].strip()
                    else:
                        # If the format is unexpected, try to determine validity from content
                        lower_response = ai_response.lower()
                        if any(phrase in lower_response for phrase in ["not clear", "unclear", "invalid", "off-topic", "not relevant", "doesn't answer", "does not answer", "could you clarify", "please provide", "need more information"]):
                            return False, ai_response
                        else:
                            return True, ai_response

                except Exception as e:
                    retry_count += 1
                    print(f"API call failed (attempt {retry_count}/{max_retries}): {str(e)}")

                    if retry_count < max_retries:
                        time.sleep(retry_delay)
                        retry_delay *= 2  # Exponential backoff
                    else:
                        # Fall back to basic validation if API calls fail
                        return self.fallback_validate_answer(question_data, answer)

            # This should not be reached, but just in case
            return self.fallback_validate_answer(question_data, answer)

        except Exception as e:
            print(f"Error in analyze_answer_with_ai: {e}")
            # Fall back to basic validation if there's an error
            return self.fallback_validate_answer(question_data, answer)

    def fallback_validate_answer(self, question_data, answer):
        """
        Fallback validation method when API calls fail.
        Returns a tuple of (is_valid, feedback_message)
        """
        question_type = question_data.get("type", "open")
        question_text = question_data.get("question", "")

        # Basic validation - empty answers
        if not answer or not answer.strip():
            if "clarification" in question_data:
                return False, question_data["clarification"]
            return False, "I need some information to proceed. Could you please provide an answer?"

        # Specific validation based on question type
        if question_type == "open":
            # Check for question keywords to validate context-specific answers
            lower_question = question_text.lower()
            lower_answer = answer.lower()

            # Check for questions about numbers/quantities that should have numeric responses
            if any(keyword in lower_question for keyword in ["how many", "number of", "quantity", "count", "size", "capacity"]):
                if not any(char.isdigit() for char in answer):
                    return False, "I was expecting a numeric value in your answer. Could you please provide a number or range?"

            # Check for time-related questions
            if any(keyword in lower_question for keyword in ["when", "time", "period", "duration", "hours", "schedule"]):
                time_indicators = ["am", "pm", "hour", "minute", "second", "day", "week", "month", "year", "morning", "afternoon", "evening", "night"]
                if not any(indicator in lower_answer for indicator in time_indicators):
                    return False, "I was asking about timing or scheduling. Could you please specify a time period or schedule in your answer?"

            # Check for database-related questions
            if any(keyword in lower_question for keyword in ["database", "data store", "storage"]):
                db_indicators = ["sql", "nosql", "mysql", "postgresql", "mongodb", "dynamodb", "aurora", "rds", "database", "redis", "cassandra", "oracle", "db"]
                if not any(indicator in lower_answer for indicator in db_indicators):
                    return False, "I was asking about database or storage solutions. Could you please specify what type of database or storage you're planning to use?"

            # Check for very short, potentially incomplete answers
            if len(answer.split()) < 3 and len(answer) < 15 and not any(char.isdigit() for char in answer):
                # Very short answer that's not a number
                return False, "Your answer seems quite brief. Could you provide more details to help me understand better?"

            # If we get here, the answer passed basic validation
            return True, question_data.get("follow_up", "Thanks for your answer!")

        elif question_type == "mcq":
            options = question_data.get("options", [])
            if options and answer not in options:
                return False, "Please select one of the provided options."
            return True, question_data.get("follow_up", "Thanks for your selection!")

        elif question_type == "yes_no":
            lower_answer = answer.lower()
            if lower_answer not in ["yes", "no", "yeah", "nope", "correct", "incorrect", "definitely", "absolutely"]:
                return False, "I was expecting a 'Yes' or 'No' answer. Could you please clarify?"
            return True, question_data.get("follow_up", "Thanks for your answer!")

        # Default case
        return True, question_data.get("follow_up", "Thanks for your answer!")

    def submit_dynamic_answer(self):
        """Process the user's answer to the current open-ended question"""
        if self.current_question_index < len(self.dynamic_questions):
            answer = self.answer_input.toPlainText().strip()
            self.process_answer(answer)

    def skip_dynamic_question(self):
        """Skip the current question"""
        if self.current_question_index < len(self.dynamic_questions):
            question_data = self.dynamic_questions[self.current_question_index]

            # Add a skipped message to the chat history
            self.chat_history.append(f"<div style='color: #333333; background-color: #F0F0F0; padding: 8px; border-radius: 5px; margin: 5px 0; border-left: 3px solid #FF8C00;'><b>You:</b> <i>(Skipped)</i></div>")

            # AI response to skipping
            self.chat_history.append(f"<div style='color: #333333; background-color: #FFFFFF; padding: 8px; border-radius: 5px; margin: 5px 0; border-left: 3px solid #FF8C00; box-shadow: 0 1px 2px rgba(0,0,0,0.1);'><b>AI:</b> No problem, let's move on to the next question.</div>")

            # Store an empty answer
            question_data["answer"] = ""
            question_data["answered"] = True

            # Move to the next question
            self.current_question_index += 1

            # Display the next question or finish
            self.display_next_dynamic_question()

    def finish_dynamic_questions(self):
        """Finish the questioning process and proceed to architecture generation"""
        # Add a final message
        self.chat_history.append(f"<div style='color: #333333; background-color: #FFFFFF; padding: 10px; border-radius: 5px; margin: 5px 0; border-left: 3px solid #FF8C00; box-shadow: 0 1px 2px rgba(0,0,0,0.1);'><b>AI:</b> Thanks for providing this information! I'll now generate the optimal cloud architecture based on your requirements and answers.</div>")

        # Convert dynamic questions to the format expected by gptPrompt
        self.answers = []
        for question_data in self.dynamic_questions:
            # Only include the question text and answer in the format expected by gptPrompt
            self.answers.append({
                "question": question_data["question"],
                "answer": question_data.get("answer", "")
            })

        # Close the form after a short delay to allow the user to see the final message
        QTimer.singleShot(2000, self.formGroupBox.close)

        # Proceed to architecture generation
        self.gptPrompt()


    def displayQuestions(self):
        self.answers=[]
        self.answer_widgets=[]
        self.formGroupBox = QGroupBox()
        self.scroll_area = QScrollArea(self)
        self.scroll_area.setWidgetResizable(True)
        self.content_widget = QWidget(self.scroll_area)
        self.scroll_area.setWidget(self.content_widget)
        self.layout = QVBoxLayout(self.content_widget)
        seperator_list = {2:"Data Storage and Retrieval",4:"Compute Requirements",6:"Networking",8:"Scalability and Elasticity",9:"Security",11:"Integration and Interoperability",12:"Logging",13:"Cost Considerations",15:"User Experience",17:"Data Transfer and Bandwidth"}

        for index,question in enumerate(questionList):
            label = QLabel("Q"+str(index+1)+"- "+question.question)
            label.setStyleSheet("font-weight: bold;")
            answer_widget = self.create_answer_widget(question.question_type)
            self.answer_widgets.append(answer_widget)
            question_layout = QVBoxLayout()
            if(index==0):
                #info=QLabel("Please answer a few questions to improve our understanding of your requirement. If you dont want to reply, pleace click on Skip.")
                #question_layout.addWidget(info)
                heading = QLabel("Please answer a few questions to improve our understanding of your \nrequirement. If you dont want to reply, pleace click on Skip.")
                question_layout.addWidget(heading)
                heading.setStyleSheet("font-weight: bold;;color:orange;font-size:15px")
                question_layout.setAlignment(heading, QtCore.Qt.AlignCenter)
            question_layout.addWidget(label)
            question_layout.addWidget(answer_widget)
            self.layout.addLayout(question_layout)

            if(index in seperator_list):
                line = QLabel()
                line.setFixedHeight(2)
                line.setStyleSheet("background-color: white;")
                question_layout.addWidget(line)
                heading = QLabel(seperator_list[index])
                question_layout.addWidget(heading)
                heading.setStyleSheet("font-weight: bold;color:orange;font-size:15px")
                question_layout.setAlignment(heading, QtCore.Qt.AlignCenter)
            else:
                line = QLabel()
                line.setFixedHeight(1)
                line.setStyleSheet("background-color: orange;")
                question_layout.addWidget(line)



        submit_layout = QHBoxLayout()
        submit_button = QPushButton('Submit',self)
        #submit_button.setFixedWidth(100)
        submit_button.clicked.connect(self.submit_question_form)
        submit_layout.addWidget(submit_button,alignment=Qt.AlignRight)

        #skip_layout = QVBoxLayout()
        skip_button = QPushButton('Skip',self)
        #skip_button.setFixedWidth(100)
        skip_button.clicked.connect(self.gptPrompt)
        submit_layout.addWidget(skip_button,alignment=Qt.AlignRight)

        self.formGroupBox.setFixedWidth(700)
        self.formGroupBox.setFixedHeight(500)
        self.content_widget.setLayout(self.layout)
        self.formGroupBox.setLayout(QVBoxLayout(self))
        self.formGroupBox.layout().addWidget(self.scroll_area)
        self.formGroupBox.layout().addLayout(submit_layout)
        #self.formGroupBox.layout().addWidget(submit_button)
        self.formGroupBox.show() #self.layout.addWidget()

    def create_answer_widget(self, question_type):
        if question_type == "TEXT":
            multiline_edit = QTextEdit(self)
            multiline_edit.setFixedHeight(40)
            return multiline_edit
        elif question_type == "BINARY":
            binary_group = QButtonGroup(self)
            radio_widget = QWidget(self)
            radio_layout = QHBoxLayout(radio_widget)
            radio_button_yes = QRadioButton("Yes")
            radio_button_no = QRadioButton("No")
            binary_group.addButton(radio_button_yes)
            binary_group.addButton(radio_button_no)
            radio_layout.addWidget(radio_button_yes)
            radio_layout.addWidget(radio_button_no)
            return radio_widget
        else:
            options = question_type.split('__')[1].split(',')
            combo_box = QComboBox(self)
            combo_box.addItems(options)
            return combo_box

    def submit_question_form(self):
        self.answers = []
        count=0
        for index, question in enumerate(questionList):
            answer_widget = self.answer_widgets[index]

            if isinstance(answer_widget, QTextEdit):
                answer = answer_widget.toPlainText()
                if(answer==""):
                    count+=1
            elif isinstance(answer_widget, QWidget) and question.question_type == "BINARY":
                radio_buttons = answer_widget.findChildren(QRadioButton)
                checked_button = next((button for button in radio_buttons if button.isChecked()), None)
                if checked_button is not None:
                    answer = checked_button.text()
                else:
                    answer = ""
                    count+=1
            elif isinstance(answer_widget, QComboBox):
                answer = answer_widget.currentText()
                if(answer==""):
                    count+=1

            self.answers.append({"question": question.question, "answer": answer})

        # Do something with the answers (e.g., print or process them)
        print("Confidence: ",(len(questionList)-count)/len(questionList))

        self.gptPrompt()



    def expandResultArea(self):
        self.formGroupBox1 = QGroupBox()
        self.layoutIn1 = QFormLayout()
        #t1In = QTextEdit()
        #t1In.setStyleSheet("QGroupBox { border: 1px solid orange; margin : 1px}")
        t2In = QTextEdit()
        t2In.setStyleSheet("QGroupBox { border: 1px solid orange; margin : 1px}")
        #t1In.setText(t1.toPlainText())
        t2In.setText(self.t2.toPlainText())
        tbox1 = QHBoxLayout()
        #tbox1.addWidget(t1In)
        tbox1.addWidget(t2In)

        self.layoutIn1.addRow(tbox1)
        self.formGroupBox1.setFixedWidth(500)
        self.formGroupBox1.setFixedHeight(800)
        self.formGroupBox1.setLayout(self.layoutIn1)

        self.formGroupBox1.show()

    def returner(self):
      # Check which form is currently open and close it
      if hasattr(self, 'formGroupBox1') and self.formGroupBox1:
          self.formGroupBox1.close()
      elif hasattr(self, 'formGroupBoxcl') and self.formGroupBoxcl:
          self.formGroupBoxcl.close()
      return

    def clear(self):
        self.t1In.setText("")

    def expandArea(self):
        self.upload=False
        self.formGroupBox1 = QGroupBox()
        self.formGroupBox1.setWindowTitle("Submit Written Requirements")
        self.formGroupBox1.setWindowIcon(QtGui.QIcon('mapleicon.png'))
        self.layoutIn1 = QFormLayout()
        self.t1In = QTextEdit()
        self.t1In.setStyleSheet("QGroupBox { border: 1px solid orange; margin : 1px}")
        #t2In = QTextEdit()
        #t2In.setStyleSheet("QGroupBox { border: 1px solid orange; margin : 1px}")
        self.t1In.setText(self.defaultquery)
        #t2In.setText(t2.toPlainText())
        tbox1 = QHBoxLayout()
        tbox1.addWidget(self.t1In)
        #tbox1.addWidget(t2In)
        self.c11 = QLabel("Please enter your Requirementss:")
        self.c11.setAlignment(Qt.AlignCenter)
        self.layoutIn1.addRow(self.c11)
        self.layoutIn1.addRow(self.t1In)
        self.formGroupBox1.setFixedWidth(1000)
        self.formGroupBox1.setFixedHeight(500)
        self.formGroupBox1.setLayout(self.layoutIn1)
        ok_button = QPushButton("Submit")
        clear_button=QPushButton("Clear")
        cancel_button = QPushButton("Cancel")
        ok_button.clicked.connect(self.displayQuestions)
        clear_button.clicked.connect(self.clear)
        cancel_button.clicked.connect(self.returner)
        HBox = QHBoxLayout()
        HBox.addWidget(clear_button)
        HBox.addWidget(ok_button)
        HBox.addWidget(cancel_button)
        self.layoutIn1.addRow(HBox)
        self.formGroupBox1.setLayout(self.layoutIn1)
        self.formGroupBox1.show()


    def askingQuery(self):
        labelmarquee.setText(" :: Enter all business requirements and click 'Submit your Query' and receive the response from AI in adjacent box.")

    def populateCSPservices(self, item):
        #itt = domainlist.currentItem()
        #print(item.text())
        selected_option = combocsp.currentText()
        #print(combocsp.currentText())
        widget5.clear()
        widget5.setEnabled(True)
        if selected_option == "AWS":
            for item in awslist[1:]:
                widget5.addItem(item)
        elif selected_option == "GCP":
            for item in gcplist[1:]:
                widget5.addItem(item)
        elif selected_option == "AZURE":
            for item in azurelist[1:]:
                widget5.addItem(item)

    def listwidgetdoubleclicked(self, item):
        try:
            model = item.data(CustomObjectRole)
        except TypeError:
            return
        attrs = copy.deepcopy(vars(model))
        attrs.pop('InitialCores')
        attrs.pop('InitialHardware')
        attrs.pop('InitialLatency')
        attrs.pop('InitialCost')
        msg = QMessageBox()
        msg.setWindowTitle("Model Details")
        msg.setWindowIcon(QtGui.QIcon('mapleicon.png'))
        msg.setText(''.join("%s: %s\n" % item for item in attrs.items()))
        x = msg.exec_()

#from collections import deque
    #from collections import deque

    #from collections import deque

    from collections import deque

    def edges_to_adjacency_list(self,edges):
        adjacency_list = {}  # Stores the final adjacency list
        in_degree = {}  # Tracks incoming edges for cycle detection

        # Step 1: Build initial adjacency list and in-degree count
        print("Input edges:", edges)
        for u, v in edges:
            if u not in adjacency_list:
                adjacency_list[u] = []
                in_degree[u] = 0  # Initialize in-degree

            if v not in adjacency_list:
                adjacency_list[v] = []
                in_degree[v] = 0  # Initialize in-degree

            # Prevent self-loops (u → u)
            if u == v:
                continue

            # Add edge if it doesn't create a duplicate
            if v not in adjacency_list[u]:
                adjacency_list[u].append(v)
                in_degree[v] += 1  # Increase in-degree for cycle detection

        print("Initial Adjacency List:", adjacency_list)
        '''
        # Step 2: Detect and remove cycles using Kahn's Algorithm
        queue = deque([node for node in in_degree if in_degree[node] == 0])  # Start with nodes having no incoming edges
        acyclic_adjacency_list = {node: [] for node in adjacency_list}  # Initialize with all nodes

        visited_nodes = set()

        while queue:
            node = queue.popleft()
            visited_nodes.add(node)

            for neighbor in adjacency_list[node]:
                in_degree[neighbor] -= 1  # Reduce in-degree
                if in_degree[neighbor] == 0:
                    queue.append(neighbor)  # Add new independent nodes
                acyclic_adjacency_list[node].append(neighbor)
        '''
    # Step 3: Handle cycles
    #remaining_nodes = set(adjacency_list.keys()) - visited_nodes
    #if remaining_nodes:
    #    print(f"Cycle detected! Nodes involved: {remaining_nodes}")
    #    return adjacency_list  # Returning the original adjacency list to preserve all nodes

    #return acyclic_adjacency_list
        acyclic_adjacency_list=adjacency_list
        correct_acyclic_adjacency_list={}
        dones=[]
        print("origgg")
        print(acyclic_adjacency_list)
        donecounter={}
        for k,v in acyclic_adjacency_list.items():
            newvals=[]
            for vals in v:
                if k!=vals:
                    if vals not in dones:
                        newvals.append(vals)
                    else:
                        counter=donecounter[vals]+1
                        newvals.append(vals+"_"+str(counter))
                        donecounter[vals]+=1

            if len(newvals)>0:
                nk=k
                if k in dones:
                    nk=k+"_"+str(donecounter[vals])
                correct_acyclic_adjacency_list[k]=newvals
                dones.append(k)
                donecounter[k]=0
        print(acyclic_adjacency_list)
        print(correct_acyclic_adjacency_list)
        return correct_acyclic_adjacency_list  # Returns a clean, cycle-free adjacency list


    def plotGraph(self,network):
        buttonClear.setEnabled(True)
        #serviceList = [7,10,11,97,98]#t2.toPlainText().splitlines()
        rownum = 0
        #serviceList = self.t2.toPlainText().splitlines()
        serviceList = network.splitlines()
        edgeslist=[]
        for ee in serviceList:
            pair=ee.split("-")
            edgeslist.append((pair[0].strip(),pair[1].strip()))
        adj_list=self.edges_to_adjacency_list(edgeslist)
        #collShape = []
        print("==============================================")
        print(adj_list)
        shapedict={}
        for node, neighbors in adj_list.items():
            import builtins
            newneighbors = builtins.list(neighbors)
            #newneighbors=list(neighbors)
            newneighbors.append(node)
            print("newneighbours")
            print(newneighbors)
            for servi in newneighbors:
                rownum = 0
                print("servi")
                print(servi)
                if servi in shapedict:
                   continue
                rownum = 0
                print("serviiiii")
                print(servi)
                print(servi)
                serv=servi.split("_")[0]
                for iterator in awslist:
                    rownum += 1
                    #print(iterator)
                    print("iterator..")
                    print(iterator)
                    print(serv.replace("- ", ""))
                    print(" ".join(serv.split(" ")[1:]).strip())
                    if serv.replace("- ", "") == iterator or (serv!="User" and " ".join(serv.split(" ")[1:]).strip() in iterator):
                        print("Here in PlotGraph 1"+serv)
                        print(rownum)
                        print(iterator)
                        shape = Shape("CSP",50,50,0,0,rownum)
                        shapedict[servi]=shape
                        #collShape.append(shape)
                        break
                    elif serv.replace("- ", "") == iterator and (serv=="User"):
                        print("Here in PlotGraph 2"+serv)
                        print(rownum)
                        print(iterator)
                        shape = Shape("CSP",50,50,0,0,rownum)
                        shapedict[servi]=shape
                        #collShape.append(shape)
                        break

        print(adj_list)
        import sys
        #sys.exit()
        diagram.plotOnCanvas2(adj_list,shapedict) #for x in collShape:

        #diagram.calcCostLatency()
        #    print(x.a)

    def tempfunc1(self): #hardcoding for element 'USER'
        shape = Shape("CSP",50,50,0,0,237)
        diagram.addObject(shape)

    def tempfunc2(self): #hardcoding for element 'USER'
        shape = Shape("CSP",50,50,0,0,238)
        diagram.addObject(shape)

    def firstElement(self): #hardcoding for element 'USER'
        shape = Shape("CSP",50,50,0,0,234)
        diagram.addObject(shape)
        #diagram.plotOnCanvas(t2.toPlainText()) #shape.setPos(QPointF(100,100))
        labelmarquee.setText(" :: To identify next suitable cloud service please  Connect with AI or select pre-decided service from left side pallete and plot on canvas using double click.")

    def listServiceClicked(self, item): #print(item.text()) rownum = 0
        selected_option = combocsp.currentText()
        rownum = 0
        print(item.text())
        if selected_option == "AWS":
            for iterator in awslist:
                rownum += 1
                if item.text() == iterator:
                    #.split('^')[0]: #diagram.addObject("CSP",100,50,0,0,iterator)
                    shape = Shape("CSP",100,50,0,0,rownum)
                    #shape = Shape("CSP",100,50,0,0,iterator)
                    diagram.addObject(shape)
                    print(">>>>"+shape.name)
        elif selected_option == "GCP":
            for iterator in gcplist:
                rownum += 1
                if item.text() == iterator:
                    #.split('^')[0]: #diagram.addObject("CSP",100,50,0,0,iterator)
                    shape = Shape("GCP",100,50,0,0,rownum)
                    #shape = Shape("CSP",100,50,0,0,iterator)
                    diagram.addObject(shape)
        elif selected_option == "AZURE":
            for iterator in azurelist:
                rownum += 1
                if item.text() == iterator:
                    #.split('^')[0]: #diagram.addObject("CSP",100,50,0,0,iterator)
                    shape = Shape("AZR",100,50,0,0,rownum)
                    #shape = Shape("CSP",100,50,0,0,iterator)
                    diagram.addObject(shape)
        '''
        for iterator in awslist:
            rownum += 1
            if item.text() == iterator:
                #.split('^')[0]: #diagram.addObject("CSP",100,50,0,0,iterator)
                shape = Shape("CSP",100,50,0,0,rownum)
                #shape = Shape("CSP",100,50,0,0,iterator)
                diagram.addObject(shape)
        '''
    #widget5.addItem(item.text()) #button5.setEnabled(True)

    def nextServiceSelected(self, service):
        for iterator in awslist:
            if service == iterator.split('^')[0]:
                #diagram.addObject("CSP",100,50,0,0,iterator)
                shape = Shape("CSP",100,50,0,0,iterator)
                diagram.addObject(shape)
                print("Selected the Service") #widget5.addItem(item.text())

        #diagram.addObject("CSP",100,50,0,0,item.text())
        #diagram.addObject("CSP",100,50,0,0,iterator)

    def listwidgetclicked(self, item):
        # Commented out to fix syntax errors
        # button1.setChecked(False)
        # button2.setChecked(False)
        # if(button7.isChecked()):
        #     self.allmodels.editModel(item)
        # else:
        model = copy.deepcopy(item.data(CustomObjectRole))
        diagram.addObjectModel(model)

    def storageListWidgetClicked(self, storageType):
        diagram.addStorage()

    def tfrListClicked(self,item):
        # Commented out to fix syntax errors
        # if(edit_tfr_button.isChecked()):
        #     self.tfrlist.editTfr(item)
        # else:
        pass

    def buttonClearClicked(self):
        #button2.setEnabled(False)
        button5.setEnabled(False)
        #buttonsave.setEnabled(False)
        buttonClear.setEnabled(False)
        #widget5.setEnabled(False)
        modelsBox.setEnabled(False)
        costlatencybutton.setEnabled(False)
        #deploybutton.setEnabled(False)
        buttonsave.setEnabled(False)
        #DR.setEnabled(False)
        labelmarquee.setText(" Tip :: Use 'Input' button(& double click on canvas) to add 'Input' element in canvas or Use 'Load Logic' button to load previously saved logic.")
        global list
        list.clear()
        self.le_search.clear()
        #self.find_item()
        diagram.scene.clear()
        diagram.adjacency_matrix=[]
        #diagram.calcCostLatency()
        global vpcdepbox
        for button in self.vpcbuttons:
            vpcdepbox.removeWidget(button)
            button.setParent(None)  # Detach the widget from its parent
            button.deleteLater()
        diagram.vpclist={}
        diagram.vpcsquares={}
        diagram.vpcval=0
        global vpccount
        vpccount=0
        totCostDisp.setText("-- $")
        totLateDisp.setText("-- sec")

    def buttonCloudClicked(self): #For cost of deployment to cloud
        msg = QMessageBox()
        msg.setWindowTitle("Deployment Cost (Cloud)")
        cost = diagram.calcCloudCost()
        lat = diagram.calcCloudLatency()
        #lat = diagram.getNetCloudLatency()
        msg.setText("Total Cost(Cloud) per request: " + str(cost) + "$\n"+"Total Cost(Cloud) for 1000 requests: " + str(cost*1000) + "$"+"\nTotal Latency(Cloud) per request: " + str(lat) + " ms")
        x = msg.exec_()

    def cloudCostSelected(self,check):
        #buttoncloud.setVisible(check)
        global deploymentMode
        if(check):
            deploymentMode="cloud"
            #button3.setEnabled(True)
            #serverlabel.setEnabled(False)
            #buttonconfig.setEnabled(False)
            #widget6.setEnabled(False)
            label8.setText("")
        labelmarquee.setText(" Tip :: Right click on model element and set cloud deployment cost as per requirement. Check predicted Cost & Latency.")

    def dispTotCost(self):
        if(deploymentMode=="cloud"):
            msg = QMessageBox()
            msg.setWindowTitle("Deployment Cost (Cloud)")
            cost = diagram.calcCloudCost()
            lat = diagram.calcCloudLatency()
            #lat = diagram.getNetCloudLatency()
            msg.setText("Total Cost(Cloud) per request: " + str(cost) + "$\n"+"Total Cost(Cloud) for 1000 requests: " + str(cost*1000) + "$"+"\nTotal Latency(Cloud) per request: " + str(lat) + " ms")
            x = msg.exec_()
        elif(deploymentMode=="local"):
            msg = QMessageBox()
            msg.setWindowTitle("Total Cost") #c1 = diagram.reviseCost(16)
            cost = diagram.getTotalCost(16)
            msg.setText("Total Cost: " + str(cost) + "$")
            x =  msg.exec_()

    #Latency function

    def dispTotLat(self):
        msg = QMessageBox()
        msg.setWindowTitle("Pipeline Latency")
        lat = diagram.getNetLatency()
        msg.setText("Total Latency: " + str(lat) + " ms")
        x = msg.exec_()

    def getQTableWidgetSize(self):
        w = self.verticalHeader().width() + 4  # +4 seems to be needed
        for i in range(self.columnCount()):
            w += self.columnWidth(i)  # seems to include gridline (on my machine)
        h = self.horizontalHeader().height() + 4
        for i in range(self.rowCount()):
            h += self.rowHeight(i)
        return QtCore.QSize(w, h)

    def dispRecommendations(self):
        dicti={}
        for i in range(self.layout.count()):
            item = self.layout.itemAt(i)
            key=""
            value=""
            if item and isinstance(item.widget(),QLineEdit):
                key = self.layout.labelForField(item.widget()).text().strip()
                value = item.widget().text()
            dicti[key]=value
        #subprocess.run('export AWS_REGION='+dicti["Default Region :"], shell=True, text=True)
        #subprocess.run('export AWS_ACCESS_KEY_ID='+dicti["Access Key ID :"], shell=True, text=True)
        #subprocess.run('export AWS_SECRET_ACCESS_KEY='+dicti["Secret Access Key :"], shell=True, text=True)

        subprocess.run('terraform init', shell=True, text=True)
        subprocess.run('terraform validate', shell=True, text=True)
        subprocess.run('terraform apply -auto-approve', shell=True, text=True)


    def deployArch(self):
        self.archBox = QGroupBox()
        self.archlayout = QFormLayout()
        #self.setLayout(layout)

        secrete_access_key = QLineEdit()
        access_key = QLineEdit()
        region = QLineEdit()
        self.archlayout.addRow('Secret Access Key :', secrete_access_key )
        self.archlayout.addRow('Access Key ID :', access_key)
        self.archlayout.addRow('Default Region :', region)
        depbutton = QPushButton('Deploy')
        self.archlayout.addRow(depbutton)
        depbutton.clicked.connect(self.dispRecommendations)
        self.archBox.setLayout(self.archlayout)
        self.archBox.show()


    def checktype(self):

        radio_buttons = self.radio_widgettype.findChildren(QRadioButton)
        checked_button = next((button for button in radio_buttons if button.isChecked()), None)
        if checked_button is not None:
            answer = checked_button.text()
        else:
            answer = ""
            count+=1
        if len(answer)>0:
            self.dispTotCostLatencyfinal(answer)

        self.formGroupBoxcl.close()

    def dispTotCostLatency(self):
        if diagram.archtype==None:
            currServices = []
            self.formGroupBoxcl = QGroupBox()
            question_layout = QVBoxLayout()
            heading = QLabel("Please select kind of architechture you have: ")
            question_layout.addWidget(heading)
            binary_group = QButtonGroup(self)
            self.radio_widgettype = QWidget(self)
            radio_layout = QHBoxLayout(self.radio_widgettype)
            radio_button_yes = QRadioButton("Monolith")
            radio_button_no = QRadioButton("Microservices")
            binary_group.addButton(radio_button_yes)
            binary_group.addButton(radio_button_no)
            radio_layout.addWidget(radio_button_yes)
            radio_layout.addWidget(radio_button_no)
            question_layout.addWidget(self.radio_widgettype)
            ok_button = QPushButton("Submit")
            cancel_button = QPushButton("Cancel")
            ok_button.clicked.connect(self.checktype)
            cancel_button.clicked.connect(self.returner)
            h_layout_widget = QWidget()
            h_layout_widget.setLayout(question_layout)
            self.formGroupBoxcl.setLayout(QVBoxLayout(self))
            self.formGroupBoxcl.layout().addWidget(h_layout_widget)
            submit_layout = QHBoxLayout()
            submit_layout.addWidget(ok_button,alignment=Qt.AlignRight)
            h_layout_widget = QWidget()
            h_layout_widget.setLayout(submit_layout)
            self.formGroupBoxcl.layout().addWidget(h_layout_widget)
            cancel_layout = QHBoxLayout()
            cancel_layout.addWidget(cancel_button,alignment=Qt.AlignRight)
            h_layout_widget = QWidget()
            h_layout_widget.setLayout(cancel_layout)
            self.formGroupBoxcl.layout().addWidget(h_layout_widget)
            self.formGroupBoxcl.show()
        else:
            global typenow
            typenow=diagram.archtype
            diagram.setserv2(typenow)



    def dispTotCostLatencyfinal(self, type):
        currServices = []
        global typenow
        typenow=type
        self.formGroupBoxc2 = QGroupBox()
        self.formGroupBoxc2.setMaximumSize(800, 350)  # Adjusted maximum size for a better fit
        self.formGroupBoxc2.setMinimumSize(200, 100)  # Set a minimum size to ensure content fits

        question_layout = QVBoxLayout()
        diagram.archtype=type

        #if type == "Microservices":
        #    if len(diagram.mappings)==0:
        #        diagram.anotherform()
        #    else:
        #        diagram.anotherform3()
        #else:
        diagram.setserv2(type)


    def toggle_all(self, checked, service_checkboxes):
        # Toggle all checkboxes based on the 'Select All' checkbox.
        for checkbox in service_checkboxes:
            checkbox.setChecked(checked)

    def dispTotCostLatency2(self):
        prevCode = True
        if(deploymentMode=="cloud" and prevCode):
            self.mdbox = QGroupBox()#"Add new model to database")
            self.mdbox.setWindowTitle('Total Cost & Latency')
            self.mdbox.setWindowIcon(QtGui.QIcon('mapleicon.png'))
            self.mdlayout = QFormLayout()
            cost = diagram.calcCloudCost()
            lat = diagram.getNetCloudLatency()
            self.explainibility = QPushButton("   Explainibility   ")
            self.explainibility.clicked.connect(self.dispExplainibility)
            self.recommendation = QPushButton("   Recommendations   ")
            self.recommendation.clicked.connect(self.dispRecommendations)
            self.mdlayout.addRow(QLabel("Total Cost(Serverless) per request($)        "),QLabel(str(round(cost,6))))
            self.mdlayout.addRow(QLabel("Total Cost(Serverless) for 1000 requests($)  "),QLabel(str(round(cost*1000,6))))
            self.mdlayout.addRow(QLabel("Execution Time(Serverless) per request(secs) "),QLabel(str(lat)))
            self.mdlayout.addRow(self.explainibility,self.recommendation)
            self.mdbox.setLayout(self.mdlayout)
            self.mdbox.show()
        elif(deploymentMode=="cloud" and not prevCode):
            ami = "ami-0f5ee92e2d63afc18"
            instance_type = "t2.micro"
            region  = "ap-south-1"
            # registerEC2Resources(ami,instance_type)
            with open("terraform_aws.tf",'a') as tf_file:
                tf_file.write('provider "aws" {region  = "ap-south-1"}\n')
                tf_file.write('resource "aws_instance" "app_server"{\nami = "'+ami+'"\ninstance_type = "'+instance_type+'"\n}\n')
            tf = Terraform(working_dir=r'C:\Users\<USER>\Downloads\MapleGUI (1)\MapleGUI')
            tf.init()
            print("Terraform Intiated")
            tf.plan()
            print("Terraform plan created \nApplying Plan...")
            print(tf.apply(skip_plan=True))
            print("Plan Applied!!")
        elif(deploymentMode=="local"):
            msg = QMessageBox()
            msg.setWindowTitle("Predicted Cost & Latency")
            msg.setWindowIcon(QtGui.QIcon('mapleicon.png'))
            #Extrapolation of Cost and Latency both taken care in getTotalCost
            try:
                #cost = diagram.getTotalCost(expectedCores)
                #lat = diagram.getNetLatency()
                cost = 0
                lat = 0
                if len(diagram.scene.selectedItems()) == 0:
                    qm = QMessageBox()
                    qm.setWindowTitle("No Service Selected")
                    qm.setWindowIcon(QtGui.QIcon('mapleicon.png'))
                    reply = qm.information(self,'', "You have not selected any service to display cost and latency. Please select some service.")
                    #if reply == qm.Yes:
                        #for item in diagram.scene.items():
                            #if isinstance(item,Shape):
                                #print('Shape Item Found')
                                #cost += item.Cost
                                #lat += item.Latency # latency cannot be directly added, for parallel operaions
                        #msg.setText("     Total Cost: " + str(cost) + "$    \n     Execution Time: " + str(lat) + " secs")
                        #msg.addButton(msg.Ok)
                        #explainibility = msg.addButton("   Explainibility   ", msg.ActionRole)
                        #explainibility.clicked.connect(self.dispExplainibility)
                        #x = msg.exec_()
                else:
                    for item in diagram.scene.selectedItems():
                        if isinstance(item,Shape):
                            print('Shape Item Found')
                            cost += item.Cost
                            lat += item.Latency # latency cannot be directly added, for parallel operaions
                    msg.setText("     Total Cost: " + str(round(cost,3)) + "$    \n     Execution Time: " + str(round(lat,3)) + " secs")
                    msg.addButton(msg.Ok)
                    #explainibility = msg.addButton("   Explainibility   ", msg.ActionRole)
                    #explainibility.clicked.connect(self.dispExplainibility)
                    x = msg.exec_()
            except NameError:
                err = QMessageBox()
                err.setIcon(QMessageBox.Critical)
                err.setText("Error")
                err.setInformativeText('Server Configuration is not selected')
                err.setWindowTitle("Error")
                err.exec_()
    '''
    def closeConfigForm(self):
        self.ConfigBox.hide()

    def gpuchecked(self,bool): #shows option for gpu type only when gpu option
    is checked l3.setVisible(bool) e3.setVisible(bool)

    def selectedItem(self, item): sconfig = item.data(CustomObjectRole) attrs =
    copy.deepcopy(vars(sconfig)) if(attrs["GPUModel"] == ""):
    attrs.pop('GPUModel')

    def dispConfigDet(self,item): #displays details of server configuration
    sconfig = item.data(CustomObjectRole) attrs = copy.deepcopy(vars(sconfig))
    if(attrs["GPUModel"] == ""): attrs.pop('GPUModel') global firstine
    firstline=False line=" Applied Config :: " for item in attrs.items():
    if(firstline): line = line + (" %s: %s " % item) else: global firstine
    firstline=True #print(attrs["Cores"]) global expectedCores expectedCores =
    attrs["Cores"] label8.setText(line)

    #Application functions

    def addApp(self):  #connected with Add Application button, form for adding
    application from user input num = 0 domain = "" for i in
    range(domainlist.count()): if(num > 1): break
    if(domainlist.item(i).checkState()==2): domain = domainlist.item(i).text()
    num += 1 if(num != 1): msg = QMessageBox() msg.setWindowTitle("Error
    Message") msg.setText("Select exactly one domain to create application")
    msg.exec_() return self.appbox = QGroupBox() layout = QFormLayout()
    appmodellist = QListWidget()

    '''
    #sql = ''' SELECT * FROM models WHERE Domain=? '''
    '''
    c.execute(sql,(domain,)) i = 1 for row in c: item =
    QtWidgets.QListWidgetItem("   #" + str(i) + ": " + row[Model])
    item.setFlags(QtCore.Qt.ItemIsUserCheckable | QtCore.Qt.ItemIsEnabled)
    item.setCheckState(QtCore.Qt.Unchecked) appmodellist.addItem(item) model =
    UniqueModel("#" + str(i) + ": " + row[Model], row[Model], row[Hardware],
    row[Hardware], row[Cores], row[Cores], int(row[Memory]),
    float(row[Latency]), float(row[Latency]), row[Cost], row[Cost], "Not
    Selected", row[Domain], row[Library], "Not Selected", 0.0, 0.0, "None")
    item.setData(CustomObjectRole, model) i += 1 l1= QLabel("Applicable
    Models") layout.addRow(l1) layout.addRow(appmodellist) l2 =
    QLabel("Application Name") le = QLineEdit() layout.addRow(l2,le) ok =
    QPushButton("Save Application") cancel = QPushButton("Cancel")
    layout.addRow(ok, cancel) ok.clicked.connect(lambda x:
    self.saveApp(appmodellist,le.text())) cancel.clicked.connect(self.closeApp)
    self.appbox.setLayout(layout) self.appbox.show()

    def saveApp(self,appmodellist,name): self.savedapps.addItem(name + ":") for
    i in range(appmodellist.count()): if(appmodellist.item(i).checkState()==2):
    item = QListWidgetItem(appmodellist.item(i).text())
    self.savedapps.addItem(item) item.setData(CustomObjectRole,
    appmodellist.item(i).data(CustomObjectRole))

        self.appbox.hide()

    def closeApp(self): self.appbox.hide()

    #Save/Load functions

    def save(self,applist,configlist): #save applications, configs and diagram
    into CSV file options = QFileDialog.Options() options |=
    QFileDialog.DontUseNativeDialog fileName, _ =
    QFileDialog.getSaveFileName(diagram,"Save Logic","","CSV File (*.csv)",
    options=options) if fileName: with open(fileName, mode='w',newline='') as
    csv_file: writer = csv.writer(csv_file, delimiter=',') items =
    diagram.scene.items() for item in items: if(item.type() ==
    QGraphicsLineItem().type()): writer.writerow("A")
    writer.writerow(item.start_item.getList())
    writer.writerow(item.end_item.getList()) elif ((item.type() !=
    QGraphicsTextItem().type()) & (item.type() !=
    QGraphicsPixmapItem().type())): if((item.in_arrows == []) &
    (item.out_arrows == [])): writer.writerow(item.getList())

                approw = "" for i in range(applist.count()): if(approw != ""
                and applist.item(i).text()[0] != " "):
                csv_file.write("Application," + approw[:-1:] + "\n") approw =
                applist.item(i).text() + "," else: approw +=
                applist.item(i).text() + "," if(approw != ""):
                csv_file.write("Application," + approw[:-1:] + "\n")

                for i in range(configlist.count()): config =
                configlist.item(i).data(CustomObjectRole)
                writer.writerow(config.getList())

    def load(self): #load saved diagram from CSV options =
    QFileDialog.Options() options |= QFileDialog.DontUseNativeDialog fileName,
    _ = QFileDialog.getOpenFileName(diagram,"Load Logic", "","CSV File
    (*.csv)", options=options) button2.setEnabled(True)
    button5.setEnabled(True) buttonsave.setEnabled(True)
    buttonconfig.setEnabled(True) buttonClear.setEnabled(True)
    DR.setEnabled(True) widget7.setEnabled(True)
    add_tfr_button.setEnabled(True) edit_tfr_button.setEnabled(True)
    widget5.setEnabled(True) button6.setEnabled(True) button7.setEnabled(True)
    #widget6.clear() if fileName: diagram.scene.clear() with open(fileName,'r')
    as csv_file: reader = csv.reader(csv_file, delimiter=',')
    self.load_helper(reader,False,None) items = diagram.scene.items()
    list.clear() for item in items: item.setVisible(True)
    if(ifWhat.isEnabled()): labelmarquee.setText(" Tip :: Select any deployment
    resource and check prediction numbers.") else: labelmarquee.setText(" Tip
    :: Enter Cost and Latency constraints. Use 'Generate' button to get DAG and
    configuration") domainsBox.setEnabled(True) label8.setText(" Applied Config
    ::")

    def load_helper(self,reader,bool,first_item): try: while True: curr =
    next(reader) if(len(curr)>0): break except StopIteration: return
# =============================================================================
# try: type = curr.pop(0) except IndexError: return
# =============================================================================
        type = curr.pop(0) if(type == "Model"): x = float(curr.pop(0)) y =
        float(curr.pop(0)) if((str(x)+str(y)) in list): items =
        diagram.scene.items() for itemI in items: if((itemI.scenePos().x() ==
        x) & (itemI.scenePos().y() == y)): item = itemI #print(">>>>>> Item
        found in MODEL",item.scenePos().x()) else: list.append(str(x)+str(y))
        if(diagram.itemAt(x,y) is None): item =
        ModelShape(UniqueModel(curr[0],curr[1],curr[2],curr[3],int(curr[4]),int(curr[5]),int(curr[6]),float(curr[7]),float(curr[8]),float(curr[9]),float(curr[10]),curr[11],curr[12],curr[13],
        curr[14],float(curr[15]),float(curr[16]),curr[17]))
        item.textItem.setVisible(False) #to prevent interference with
        scene.itemAt function diagram.scene.addItem(item) item.setPos(x,y)
        else: item = diagram.itemAt(x,y) elif(type == "Shape"): x =
        float(curr.pop(0)) y = float(curr.pop(0)) if((str(x)+str(y)) in list):
        items = diagram.scene.items() for itemI in items:
        #print(itemI.scenePos().x()," ^^^^ ",itemI.scenePos().y())
        if((itemI.scenePos().x() == x) & (itemI.scenePos().y() == y)): item =
        itemI #print(">>>>>> Item found in SHAPE",item.scenePos().x()) else:
        list.append(str(x)+str(y)) #if(diagram.itemAt(x,y) is None): item =
        Shape(curr[0],int(curr[1]),int(curr[2]),float(curr[3]),float(curr[4]),curr[5])
        #print("********* Item Created Shape",item.scenePos().x())
        item.textItem.setVisible(False) diagram.scene.addItem(item)
        item.setPos(x,y) item.tfrs = diagram.tfrs #print("********* Item
        Created Shape IN IF",item.scenePos().x(),"^",item.scenePos().y())
        #else:
                #    ELSE",item.sce                #    item = diagram.itemAt(x,y) print("********* Item Created
                #    Shape IN
nePos().x(),"^",item.scenePos().y())
        elif(type == "A"): self.load_helper(reader,True,None) elif(type ==
        "Config"): config = Config(curr[0],int(curr[1]),int(curr[2]),curr[3])
         configitem = QtWidgets.QListWidgetItem(config.ID)
        widget6.addItem(configitem) configitem.setData(CustomObjectRole,
        config) diagram.updateConfigList(config) elif(type == "Application"):
        for item in curr: self.savedapps.addItem(item)

        if(bool == True): if(first_item is None):
        self.load_helper(reader,True,item) else:
        diagram.scene.addItem(Arrow(first_item,item))
        self.load_helper(reader,False,None) else:
        self.load_helper(reader,False,None)

        self.load_helper(reader,False,None)
    '''
