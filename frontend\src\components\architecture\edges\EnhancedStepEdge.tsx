import React from 'react'
import { EdgeProps, getSmoothStepPath, EdgeLabelRenderer, BaseEdge } from 'reactflow'

interface EnhancedStepEdgeProps extends EdgeProps {
  data?: {
    label?: string
    animated?: boolean
    strokeColor?: string
    strokeWidth?: number
    showDirection?: boolean
  }
}

export const EnhancedStepEdge: React.FC<EnhancedStepEdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  selected,
  markerEnd
}) => {
  const [edgePath, labelX, labelY] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
    borderRadius: 8,
  })

  // Calculate direction for arrow placement
  const isHorizontal = Math.abs(targetX - sourceX) > Math.abs(targetY - sourceY)
  const direction = isHorizontal 
    ? (targetX > sourceX ? '→' : '←')
    : (targetY > sourceY ? '↓' : '↑')

  // Enhanced styling for better visibility
  const edgeStyle = {
    stroke: selected ? '#EF4444' : (data?.strokeColor || '#2563EB'),
    strokeWidth: selected ? 4 : (data?.strokeWidth || 3),
    strokeLinecap: 'round' as const,
    strokeLinejoin: 'round' as const,
    filter: selected 
      ? 'drop-shadow(0 0 8px rgba(239, 68, 68, 0.5))' 
      : 'drop-shadow(0 0 4px rgba(37, 99, 235, 0.3))',
    transition: 'all 0.2s ease-in-out',
    ...style
  }

  // Enhanced marker for prominent arrows
  const enhancedMarkerEnd = {
    type: 'arrowclosed',
    color: selected ? '#EF4444' : (data?.strokeColor || '#2563EB'),
    width: selected ? 32 : 26,
    height: selected ? 32 : 26,
    ...markerEnd
  }

  return (
    <>
      <BaseEdge
        id={id}
        path={edgePath}
        style={edgeStyle}
        markerEnd={enhancedMarkerEnd}
      />
      
      {/* Data flow label */}
      {data?.label && (
        <EdgeLabelRenderer>
          <div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              fontSize: 11,
              fontWeight: 600,
              background: 'rgba(255, 255, 255, 0.95)',
              padding: '3px 8px',
              borderRadius: '6px',
              border: `2px solid ${selected ? '#EF4444' : '#2563EB'}`,
              color: selected ? '#EF4444' : '#2563EB',
              pointerEvents: 'all',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
              backdropFilter: 'blur(4px)'
            }}
            className="nodrag nopan"
          >
            {data.label}
          </div>
        </EdgeLabelRenderer>
      )}
      
      {/* Direction indicator - always visible for better UX */}
      {(data?.showDirection !== false) && (
        <EdgeLabelRenderer>
          <div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY - (data?.label ? 35 : 15)}px)`,
              fontSize: selected ? 16 : 14,
              fontWeight: 900,
              color: selected ? '#EF4444' : '#2563EB',
              pointerEvents: 'none',
              textShadow: '0 1px 3px rgba(255, 255, 255, 0.9)',
              filter: selected ? 'drop-shadow(0 0 4px rgba(239, 68, 68, 0.6))' : 'none',
              transition: 'all 0.2s ease-in-out'
            }}
            className="nodrag nopan"
          >
            {direction}
          </div>
        </EdgeLabelRenderer>
      )}
      
      {/* Animated flow indicator for selected edges */}
      {selected && (
        <EdgeLabelRenderer>
          <div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX + 20}px,${labelY - 15}px)`,
              fontSize: 12,
              fontWeight: 700,
              color: '#EF4444',
              pointerEvents: 'none',
              animation: 'pulse 1.5s ease-in-out infinite',
              textShadow: '0 1px 2px rgba(255, 255, 255, 0.8)'
            }}
            className="nodrag nopan"
          >
            ⚡
          </div>
        </EdgeLabelRenderer>
      )}
      
      <style jsx>{`
        @keyframes pulse {
          0%, 100% { opacity: 0.6; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.1); }
        }
      `}</style>
    </>
  )
}

export default EnhancedStepEdge
