import React, { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import ServiceIcon from '@/components/ui/ServiceIcon'
import {
  getServicesByProvider,
  AWS_SERVICES
} from './utils/serviceDefinitions'
import { CloudService } from '@/types/architecture'
import { Search, Plus, User, ChevronLeft, ChevronRight, Grid3X3 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface CollapsibleComponentPaletteProps {
  onServiceDragStart: (event: React.DragEvent, service: CloudService) => void
  onAddUser: () => void
  className?: string
  isCollapsed?: boolean
  onToggleCollapse?: () => void
}

export const CollapsibleComponentPalette: React.FC<CollapsibleComponentPaletteProps> = ({
  onServiceDragStart,
  onAddUser,
  className,
  isCollapsed = false,
  onToggleCollapse
}) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('All')

  // Only use AWS services
  const currentServices = useMemo(() => {
    const awsServices = getServicesByProvider('AWS')
    return awsServices.length > 0 ? awsServices : AWS_SERVICES
  }, [])

  // Get unique categories
  const categories = useMemo(() => {
    const cats = ['All', ...new Set(currentServices.map(service => service.category))]
    return cats.sort()
  }, [currentServices])

  // Filter services based on search and category
  const filteredServices = useMemo(() => {
    return currentServices.filter(service => {
      const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          service.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          service.description?.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCategory = selectedCategory === 'All' || service.category === selectedCategory
      return matchesSearch && matchesCategory
    })
  }, [currentServices, searchTerm, selectedCategory])

  const handleDragStart = (event: React.DragEvent, service: CloudService) => {
    console.log('Dragging service:', service.name)
    onServiceDragStart(event, service)
  }

  // Collapsed view - icon only
  if (isCollapsed) {
    return (
      <TooltipProvider>
        <Card className={cn('w-16 h-full flex flex-col bg-white border-r shadow-sm', className)}>
          <CardHeader className="p-2 flex-shrink-0 bg-white border-b">
            <div className="flex flex-col items-center space-y-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onToggleCollapse}
                    className="w-10 h-10 p-0"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>Expand Components Panel</p>
                </TooltipContent>
              </Tooltip>
              
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onAddUser}
                    className="w-10 h-10 p-0 text-blue-600 hover:bg-blue-50"
                  >
                    <User className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>Add User</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </CardHeader>

          <CardContent className="flex-1 overflow-y-auto p-1">
            <div className="space-y-1">
              {/* Show first few popular services as icons */}
              {currentServices.slice(0, 8).map((service, index) => (
                <Tooltip key={`${service.id}-${index}`}>
                  <TooltipTrigger asChild>
                    <div
                      draggable
                      onDragStart={(e) => handleDragStart(e, service)}
                      className="w-12 h-12 border border-orange-200 rounded bg-white hover:bg-orange-50 cursor-grab active:cursor-grabbing shadow-sm hover:shadow-md transition-all hover:scale-105 active:scale-95 flex items-center justify-center"
                    >
                      <ServiceIcon 
                        service={service} 
                        className="w-6 h-6"
                      />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p>{service.name}</p>
                  </TooltipContent>
                </Tooltip>
              ))}
              
              {/* Show more indicator */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="w-12 h-12 border border-gray-200 rounded bg-gray-50 flex items-center justify-center text-gray-400">
                    <Grid3X3 className="h-4 w-4" />
                  </div>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>Expand to see all {currentServices.length} services</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </CardContent>
        </Card>
      </TooltipProvider>
    )
  }

  // Expanded view - full functionality
  return (
    <Card className={cn('w-80 h-full flex flex-col bg-white border-r shadow-sm', className)}>
      <CardHeader className="pb-2 flex-shrink-0 bg-white border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Plus className="h-4 w-4 text-blue-600" />
            <CardTitle className="text-gray-900 text-sm">Components</CardTitle>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleCollapse}
            className="w-8 h-8 p-0"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
        </div>
        <CardDescription className="text-gray-600 text-xs">
          Drag components to the canvas to build your architecture
        </CardDescription>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col space-y-2 overflow-hidden min-h-0 p-2">
        {/* AWS Provider Header */}
        <div className="flex-shrink-0 p-2 bg-gradient-to-r from-orange-50 to-orange-100 border border-orange-200 rounded">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-orange-500 rounded flex items-center justify-center">
              <span className="text-white font-bold text-xs">AWS</span>
            </div>
            <div>
              <div className="text-xs font-bold text-orange-800">Amazon Web Services</div>
              <div className="text-xs text-orange-600">Cloud Computing Platform</div>
            </div>
          </div>
        </div>

        {/* Search */}
        <div className="flex-shrink-0 relative">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400" />
          <Input
            placeholder="Search services..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-7 h-7 text-xs bg-white border-gray-300 focus:border-orange-400 focus:ring-orange-400"
          />
        </div>

        {/* Add User Button */}
        <Button
          onClick={onAddUser}
          variant="outline"
          size="sm"
          className="flex-shrink-0 h-7 text-xs border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400"
        >
          <User className="h-3 w-3 mr-1" />
          Add User
        </Button>

        {/* Category Filter */}
        <div className="flex-shrink-0">
          <div className="flex flex-wrap gap-1">
            {categories.map((category) => (
              <Badge
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                className={cn(
                  "text-xs cursor-pointer hover:bg-orange-100 transition-colors",
                  selectedCategory === category
                    ? "bg-orange-500 text-white hover:bg-orange-600"
                    : "border-orange-300 text-orange-700 hover:border-orange-400"
                )}
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </Badge>
            ))}
          </div>
        </div>

        {/* Services List */}
        <div className="flex-1 overflow-y-auto bg-gray-50">
          <div className="p-1 space-y-1">
            {filteredServices.length > 0 ? (
              filteredServices.map((service, index) => (
                <div
                  key={`${service.id}-${index}`}
                  draggable
                  onDragStart={(e) => handleDragStart(e, service)}
                  className="p-2 border border-orange-200 rounded bg-white hover:bg-orange-50 cursor-grab active:cursor-grabbing shadow-sm hover:shadow-md transition-all hover:scale-105 active:scale-95"
                  style={{ borderLeft: '3px solid #FF9900' }}
                >
                  <div className="flex items-center space-x-2">
                    <ServiceIcon 
                      service={service} 
                      className="w-6 h-6 flex-shrink-0"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="text-xs font-medium text-gray-900 truncate">
                        {service.name}
                      </div>
                      <div className="text-xs text-gray-600 truncate">
                        {service.description}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center text-gray-500 py-4">
                <p className="text-xs">No services found</p>
                <p className="text-xs">Try adjusting your search or category filter</p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default CollapsibleComponentPalette
