#!/usr/bin/env python3
"""
Comprehensive test to verify text and icon synchronization works properly
"""

import sys
import os

def test_synchronization_implementation():
    """Test that all synchronization features are properly implemented"""
    print("=== Testing Text and Icon Synchronization Implementation ===")
    print()
    
    try:
        # Add MapleGUI to path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'MapleGUI'))
        
        # Test 1: Import all required classes
        print("1. Testing imports...")
        from Shape import Shape
        from Diagram import GraphicView
        from CSWidgets import CSWidgets
        print("   ✓ All classes imported successfully")
        
        # Test 2: Check if toggleSelection method exists
        print("2. Testing toggleSelection method...")
        if hasattr(GraphicView, 'toggleSelection'):
            print("   ✓ toggleSelection method exists in GraphicView")
        else:
            print("   ✗ toggleSelection method missing")
            return False
            
        # Test 3: Check Shape synchronization methods
        print("3. Testing Shape synchronization methods...")
        shape_methods = dir(Shape)
        
        required_methods = [
            'createServiceNameText',
            'deleteService',
            'keyPressEvent'
        ]
        
        for method in required_methods:
            if method in shape_methods:
                print(f"   ✓ {method} method exists")
            else:
                print(f"   ✗ {method} method missing")
                return False
        
        # Test 4: Check Shape attributes
        print("4. Testing Shape attributes...")
        # Create a test shape instance
        test_shape = Shape("CSP", 100, 50, 0, 0, 1)
        
        required_attributes = [
            'serviceNameText',
            'textItem'
        ]
        
        for attr in required_attributes:
            if hasattr(test_shape, attr):
                print(f"   ✓ {attr} attribute exists")
            else:
                print(f"   ✗ {attr} attribute missing")
                return False
        
        # Test 5: Test createServiceNameText functionality
        print("5. Testing createServiceNameText functionality...")
        try:
            service_text = test_shape.createServiceNameText("Test Service")
            if service_text is not None:
                print("   ✓ createServiceNameText returns valid object")
                
                # Check if it's a child item
                if service_text.parentItem() == test_shape:
                    print("   ✓ Service text is properly set as child item")
                else:
                    print("   ✗ Service text is not a child item")
                    return False
                    
                # Check backward compatibility
                if test_shape.textItem == service_text:
                    print("   ✓ Backward compatibility maintained (textItem points to serviceNameText)")
                else:
                    print("   ✗ Backward compatibility broken")
                    return False
                    
            else:
                print("   ✗ createServiceNameText returned None")
                return False
        except Exception as e:
            print(f"   ✗ Error testing createServiceNameText: {e}")
            return False
        
        # Test 6: Check flags and properties
        print("6. Testing Shape flags and properties...")
        flags = test_shape.flags()
        from PyQt5.QtWidgets import QGraphicsItem
        
        required_flags = [
            QGraphicsItem.ItemIsSelectable,
            QGraphicsItem.ItemIsMovable,
            QGraphicsItem.ItemSendsGeometryChanges,
            QGraphicsItem.ItemIsFocusable
        ]
        
        for flag in required_flags:
            if flags & flag:
                print(f"   ✓ {flag.name if hasattr(flag, 'name') else str(flag)} flag is set")
            else:
                print(f"   ✗ {flag.name if hasattr(flag, 'name') else str(flag)} flag is missing")
                return False
        
        print()
        print("🎉 All synchronization tests passed!")
        return True
        
    except ImportError as e:
        print(f"   ✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"   ✗ Unexpected error: {e}")
        return False

def print_usage_instructions():
    """Print instructions for using the synchronization features"""
    print()
    print("=== How to Use the Synchronization Features ===")
    print()
    print("1. **Moving Services:**")
    print("   - Click and drag any service on the canvas")
    print("   - Both the icon and text will move together automatically")
    print()
    print("2. **Selecting Services:**")
    print("   - Click on either the service icon or text")
    print("   - Both components will be selected together")
    print()
    print("3. **Deleting Services:**")
    print("   - Select a service (click on icon or text)")
    print("   - Press Delete or Backspace key")
    print("   - The entire service (icon + text) will be removed")
    print()
    print("4. **Key Features:**")
    print("   ✓ Text is now a child of the service icon")
    print("   ✓ Moving the service moves both icon and text")
    print("   ✓ Selecting the service selects both components")
    print("   ✓ Deleting removes the entire service")
    print("   ✓ Backward compatibility maintained")
    print()
    print("5. **Technical Details:**")
    print("   - Service text is created using createServiceNameText()")
    print("   - Text items are child items of Shape objects")
    print("   - Parent-child relationship ensures automatic synchronization")
    print("   - Delete key handling added to Shape class")
    print("   - Focus management for keyboard events")

def main():
    """Main test function"""
    success = test_synchronization_implementation()
    
    if success:
        print_usage_instructions()
        print()
        print("✅ The application is ready to use with full synchronization!")
        print()
        print("To start the application:")
        print("cd MapleGUI")
        print("python MainWindow.py")
        return 0
    else:
        print()
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
