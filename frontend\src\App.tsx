import { Routes, Route } from 'react-router-dom'
import { Toaster } from '@/components/ui/toaster'
import { AuthProvider } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import MainLayout from '@/components/layout/MainLayout'
import Dashboard from '@/pages/Dashboard'
import ArchitectureDesigner from '@/pages/ArchitectureDesigner'
import EnhancedArchitectureDesigner from '@/pages/EnhancedArchitectureDesigner'
import CostAnalysis from '@/pages/CostAnalysis'
import Documentation from '@/pages/Documentation'
import Chat from '@/pages/Chat'
import Requirements from '@/pages/Requirements'
import AdminPortal from '@/pages/AdminPortal'
import Login from '@/pages/Login'
import Register from '@/pages/Register'

function App() {
  return (
    <AuthProvider>
      <div className="min-h-screen bg-background">
        <Routes>
          {/* Public routes */}
          <Route
            path="/login"
            element={
              <ProtectedRoute requireAuth={false}>
                <Login />
              </ProtectedRoute>
            }
          />
          <Route
            path="/register"
            element={
              <ProtectedRoute requireAuth={false}>
                <Register />
              </ProtectedRoute>
            }
          />

          {/* Protected routes */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <MainLayout>
                  <Dashboard />
                </MainLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/architecture"
            element={
              <ProtectedRoute>
                <MainLayout>
                  <ArchitectureDesigner />
                </MainLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/architecture-enhanced"
            element={
              <ProtectedRoute>
                <MainLayout>
                  <EnhancedArchitectureDesigner />
                </MainLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/requirements"
            element={
              <ProtectedRoute>
                <MainLayout>
                  <Requirements />
                </MainLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/cost-analysis"
            element={
              <ProtectedRoute>
                <MainLayout>
                  <CostAnalysis />
                </MainLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/documentation"
            element={
              <ProtectedRoute>
                <MainLayout>
                  <Documentation />
                </MainLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/chat"
            element={
              <ProtectedRoute>
                <MainLayout>
                  <Chat />
                </MainLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin"
            element={
              <ProtectedRoute>
                <MainLayout>
                  <AdminPortal />
                </MainLayout>
              </ProtectedRoute>
            }
          />
        </Routes>
        <Toaster />
      </div>
    </AuthProvider>
  )
}

export default App
