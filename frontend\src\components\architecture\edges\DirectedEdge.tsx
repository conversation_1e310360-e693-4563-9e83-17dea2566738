import React from 'react'
import { EdgeProps, getBezierPath, EdgeLabelRenderer, BaseEdge } from 'reactflow'

interface DirectedEdgeProps extends EdgeProps {
  data?: {
    label?: string
    animated?: boolean
    strokeColor?: string
    strokeWidth?: number
  }
}

export const DirectedEdge: React.FC<DirectedEdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  selected,
  markerEnd
}) => {
  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  })

  // Enhanced styling for better arrow visibility
  const edgeStyle = {
    stroke: selected ? '#EF4444' : (data?.strokeColor || '#2563EB'),
    strokeWidth: selected ? 4 : (data?.strokeWidth || 3),
    strokeLinecap: 'round' as const,
    strokeLinejoin: 'round' as const,
    filter: selected ? 'drop-shadow(0 0 6px rgba(239, 68, 68, 0.4))' : 'drop-shadow(0 0 3px rgba(37, 99, 235, 0.2))',
    ...style
  }

  // Enhanced marker for better arrow visibility
  const enhancedMarkerEnd = {
    type: 'arrowclosed',
    color: selected ? '#EF4444' : (data?.strokeColor || '#2563EB'),
    width: selected ? 28 : 24,
    height: selected ? 28 : 24,
    ...markerEnd
  }

  return (
    <>
      <BaseEdge
        id={id}
        path={edgePath}
        style={edgeStyle}
        markerEnd={enhancedMarkerEnd}
      />
      
      {/* Optional label for data flow direction */}
      {data?.label && (
        <EdgeLabelRenderer>
          <div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              fontSize: 10,
              fontWeight: 600,
              background: 'rgba(255, 255, 255, 0.9)',
              padding: '2px 6px',
              borderRadius: '4px',
              border: `1px solid ${selected ? '#EF4444' : '#2563EB'}`,
              color: selected ? '#EF4444' : '#2563EB',
              pointerEvents: 'all',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
            }}
            className="nodrag nopan"
          >
            {data.label}
          </div>
        </EdgeLabelRenderer>
      )}
      
      {/* Direction indicator for enhanced visibility */}
      {selected && (
        <EdgeLabelRenderer>
          <div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY - 20}px)`,
              fontSize: 12,
              fontWeight: 700,
              color: '#EF4444',
              pointerEvents: 'none',
              textShadow: '0 1px 2px rgba(255, 255, 255, 0.8)'
            }}
            className="nodrag nopan"
          >
            →
          </div>
        </EdgeLabelRenderer>
      )}
    </>
  )
}

export default DirectedEdge
