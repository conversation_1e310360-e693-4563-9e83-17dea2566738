#!/usr/bin/env python3
"""
Simple test to check if our changes are syntactically correct
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if we can import the modified files"""
    try:
        print("Testing imports...")
        
        # Test Shape import
        from MapleGUI.Shape import Shape
        print("✓ Shape class imported successfully")
        
        # Test if new methods exist
        shape_methods = dir(Shape)
        if 'createServiceNameText' in shape_methods:
            print("✓ createServiceNameText method found")
        else:
            print("✗ createServiceNameText method not found")
            
        if 'deleteService' in shape_methods:
            print("✓ deleteService method found")
        else:
            print("✗ deleteService method not found")
            
        print("✓ All imports successful")
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("=== Simple Synchronization Test ===")
    success = test_imports()
    
    if success:
        print("\n🎉 Basic tests passed!")
        print("\nChanges made:")
        print("1. Added serviceNameText attribute to Shape class")
        print("2. Added createServiceNameText() method")
        print("3. Added deleteService() method")
        print("4. Added keyPressEvent() for Delete key handling")
        print("5. Modified itemChange() for better synchronization")
        print("6. Updated mousePressEvent() for focus handling")
        print("\nThe text and icon should now move together!")
    else:
        print("\n❌ Tests failed - please check the code")
