#!/usr/bin/env python3
"""
Icon Loading Verification Script
Verifies that all AWS service icons are properly accessible and identifies missing icons.
"""

import os
import json
import re
from pathlib import Path

def get_available_icons():
    """Get list of available AWS icons in frontend directory"""
    icons_dir = Path("frontend/public/icons/aws")
    if not icons_dir.exists():
        print(f"❌ Icons directory not found: {icons_dir}")
        return []
    
    icons = []
    for icon_file in icons_dir.glob("*.png"):
        icons.append(icon_file.name)
    
    print(f"✅ Found {len(icons)} AWS icon files")
    return sorted(icons)

def parse_service_definitions():
    """Parse serviceDefinitions.ts to extract service icon information"""
    service_file = Path("frontend/src/components/architecture/utils/serviceDefinitions.ts")
    if not service_file.exists():
        print(f"❌ Service definitions file not found: {service_file}")
        return []
    
    content = service_file.read_text(encoding='utf-8')
    
    # Extract service objects using regex
    service_pattern = r'\{\s*id:\s*[\'"]([^\'\"]+)[\'"],.*?name:\s*[\'"]([^\'\"]+)[\'"],.*?icon:\s*[\'"]([^\'\"]+)[\'"]'
    services = []
    
    for match in re.finditer(service_pattern, content, re.DOTALL):
        service_id = match.group(1)
        service_name = match.group(2)
        icon_path = match.group(3)
        
        services.append({
            'id': service_id,
            'name': service_name,
            'icon': icon_path,
            'uses_emoji': not icon_path.startswith('/icons/aws/'),
            'icon_file': icon_path.replace('/icons/aws/', '') if icon_path.startswith('/icons/aws/') else None
        })
    
    print(f"✅ Parsed {len(services)} services from definitions")
    return services

def verify_icon_loading():
    """Main verification function"""
    print("🔍 Starting Icon Loading Verification...")
    print("=" * 60)
    
    # Get available icons and services
    available_icons = get_available_icons()
    services = parse_service_definitions()
    
    # Categorize services
    services_with_icons = []
    services_with_emojis = []
    services_with_missing_icons = []
    services_with_available_icons = []
    
    for service in services:
        if service['uses_emoji']:
            services_with_emojis.append(service)
            # Check if there's an available icon for this service
            potential_icons = find_potential_icons(service['name'], available_icons)
            if potential_icons:
                service['potential_icons'] = potential_icons
                services_with_available_icons.append(service)
        else:
            services_with_icons.append(service)
            # Check if the icon file actually exists
            if service['icon_file'] not in available_icons:
                services_with_missing_icons.append(service)
    
    # Print results
    print(f"\n📊 VERIFICATION RESULTS:")
    print(f"Total services: {len(services)}")
    print(f"Services using AWS icons: {len(services_with_icons)}")
    print(f"Services using emoji icons: {len(services_with_emojis)}")
    print(f"Services with missing icon files: {len(services_with_missing_icons)}")
    print(f"Services with available icons to upgrade: {len(services_with_available_icons)}")
    
    # Detailed reports
    if services_with_missing_icons:
        print(f"\n❌ SERVICES WITH MISSING ICON FILES:")
        for service in services_with_missing_icons:
            print(f"  - {service['name']} (ID: {service['id']})")
            print(f"    Expected: {service['icon_file']}")
    
    if services_with_available_icons:
        print(f"\n🔄 SERVICES THAT CAN BE UPGRADED TO AWS ICONS:")
        for service in services_with_available_icons:
            print(f"  - {service['name']} (ID: {service['id']})")
            print(f"    Current: {service['icon']} (emoji)")
            print(f"    Available: {', '.join(service['potential_icons'])}")
    
    if services_with_emojis and not services_with_available_icons:
        print(f"\n📝 SERVICES USING EMOJI ICONS (no AWS icons available):")
        for service in services_with_emojis:
            if 'potential_icons' not in service:
                print(f"  - {service['name']} (ID: {service['id']}) - {service['icon']}")
    
    print(f"\n✅ SERVICES PROPERLY USING AWS ICONS:")
    for service in services_with_icons:
        if service['icon_file'] in available_icons:
            print(f"  - {service['name']} ✓")
    
    return {
        'total_services': len(services),
        'services_with_icons': len(services_with_icons),
        'services_with_emojis': len(services_with_emojis),
        'services_with_missing_icons': len(services_with_missing_icons),
        'services_with_available_icons': len(services_with_available_icons),
        'missing_icons': services_with_missing_icons,
        'upgradeable_services': services_with_available_icons
    }

def find_potential_icons(service_name, available_icons):
    """Find potential icon matches for a service name"""
    potential_icons = []
    
    # Clean service name for matching
    clean_name = service_name.lower()
    clean_name = clean_name.replace('amazon ', '').replace('aws ', '').replace('elastic ', '')
    
    # Look for exact or partial matches
    for icon in available_icons:
        icon_lower = icon.lower()
        
        # Direct name matching
        if clean_name in icon_lower or any(word in icon_lower for word in clean_name.split() if len(word) > 3):
            potential_icons.append(icon)
    
    return potential_icons

if __name__ == "__main__":
    results = verify_icon_loading()
    
    # Generate summary
    print(f"\n" + "=" * 60)
    print(f"📋 SUMMARY:")
    print(f"Icon coverage: {results['services_with_icons']}/{results['total_services']} services ({results['services_with_icons']/results['total_services']*100:.1f}%)")
    
    if results['services_with_missing_icons'] > 0:
        print(f"⚠️  {results['services_with_missing_icons']} services have missing icon files")
    
    if results['services_with_available_icons'] > 0:
        print(f"🔄 {results['services_with_available_icons']} services can be upgraded to use AWS icons")
    
    print(f"✅ Icon verification complete!")
