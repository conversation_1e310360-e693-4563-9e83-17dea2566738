/* React Flow Custom Styles */

.react-flow__node {
  font-family: inherit;
}

.react-flow__node.selected {
  box-shadow: 0 0 0 2px #3b82f6;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: #3b82f6;
  stroke-width: 3;
}

.react-flow__edge-path {
  stroke: #6366f1;
  stroke-width: 3;
}

.react-flow__edge .react-flow__edge-path {
  stroke: #6366f1;
  stroke-width: 3;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: #3b82f6;
  stroke-width: 4;
}

/* Arrow marker styles - Enhanced visibility */
.react-flow__arrowhead {
  fill: #6366f1 !important;
  stroke: #6366f1 !important;
  stroke-width: 1px !important;
}

.react-flow__edge.selected .react-flow__arrowhead {
  fill: #3b82f6 !important;
  stroke: #3b82f6 !important;
  stroke-width: 2px !important;
}

/* Ensure arrow markers are visible with higher specificity */
.react-flow__edge marker {
  fill: #6366f1 !important;
  stroke: #6366f1 !important;
}

.react-flow__edge.selected marker {
  fill: #3b82f6 !important;
  stroke: #3b82f6 !important;
}

/* Arrow marker definitions */
.react-flow__edge marker polygon {
  fill: #6366f1 !important;
  stroke: #6366f1 !important;
}

.react-flow__edge.selected marker polygon {
  fill: #3b82f6 !important;
  stroke: #3b82f6 !important;
}

/* Make arrows more prominent */
.react-flow__edge[data-testid*="rf__edge"] marker {
  fill: #6366f1 !important;
  stroke: #6366f1 !important;
  stroke-width: 1px !important;
}

.react-flow__edge[data-testid*="rf__edge"].selected marker {
  fill: #3b82f6 !important;
  stroke: #3b82f6 !important;
  stroke-width: 2px !important;
}

/* Ensure arrow markers are always visible */
.react-flow__edge-path {
  marker-end: url(#arrowclosed) !important;
}

/* Force arrow visibility on all edges */
.react-flow__edges .react-flow__edge path {
  marker-end: url(#arrowclosed) !important;
}

/* Additional arrow styling for manual connections */
.react-flow__connectionline {
  stroke: #6366f1;
  stroke-width: 3;
  marker-end: url(#arrowclosed);
}

.react-flow__edge.animated .react-flow__edge-path {
  stroke-dasharray: 5;
  animation: dashdraw 0.5s linear infinite;
}

.react-flow__edge-textbg {
  fill: white;
  fill-opacity: 0.8;
}

.react-flow__edge-text {
  font-size: 12px;
  fill: #374151;
}

.react-flow__controls {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
}

.react-flow__controls-button {
  background: white;
  border: none;
  border-bottom: 1px solid #e5e7eb;
  color: #374151;
  font-size: 16px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.react-flow__controls-button:hover {
  background: #f3f4f6;
  color: #111827;
}

.react-flow__controls-button:last-child {
  border-bottom: none;
  border-radius: 0 0 8px 8px;
}

.react-flow__controls-button:first-child {
  border-radius: 8px 8px 0 0;
}

.react-flow__minimap {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.react-flow__background {
  background-color: #ffffff;
}

.react-flow__background.dots {
  background-image: radial-gradient(circle, #e5e5e5 1px, transparent 1px);
}

/* Custom compact node styles */
.aws-service-node {
  background: white;
  border: 2px solid #ff9900;
  border-radius: 12px;
  padding: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.aws-service-node:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: scale(1.05);
  border-color: #ff7700;
}

.aws-service-node.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3), 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: scale(1.1);
}

.gcp-service-node {
  background: white;
  border: 2px solid #4285f4;
  border-radius: 12px;
  padding: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.gcp-service-node:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: scale(1.05);
}

.gcp-service-node.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3), 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: scale(1.1);
}

.azure-service-node {
  background: white;
  border: 2px solid #0078d4;
  border-radius: 12px;
  padding: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.azure-service-node:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: scale(1.05);
}

.azure-service-node.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3), 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: scale(1.1);
}

.user-node {
  background: white;
  border: 2px solid #8b5cf6;
  border-radius: 12px;
  padding: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.user-node:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: scale(1.05);
  border-color: #7c3aed;
}

.user-node.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3), 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: scale(1.1);
}

/* Enhanced Handle styles for compact nodes */
.react-flow__handle {
  width: 12px;
  height: 12px;
  border: 2px solid white;
  background: #6b7280;
  border-radius: 50%;
  transition: all 0.2s;
  opacity: 0.8;
}

.react-flow__handle:hover {
  background: #374151;
  transform: scale(1.3);
  opacity: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.react-flow__handle.connecting {
  background: #3b82f6;
  transform: scale(1.2);
  opacity: 1;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.react-flow__handle.valid {
  background: #10b981;
  transform: scale(1.2);
  opacity: 1;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3);
}

/* Enhanced directed edge animations */
@keyframes dashdraw {
  to {
    stroke-dashoffset: -10;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes flow {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: 20;
  }
}

/* Enhanced arrow visibility for directed edges */
.react-flow__edge[data-id*="edge"] {
  filter: drop-shadow(0 0 3px rgba(37, 99, 235, 0.2));
}

.react-flow__edge[data-id*="edge"].selected {
  filter: drop-shadow(0 0 6px rgba(239, 68, 68, 0.4));
}

/* Direction indicators */
.direction-indicator {
  font-weight: 900;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.9);
  transition: all 0.2s ease-in-out;
  pointer-events: none;
}

.direction-indicator.selected {
  filter: drop-shadow(0 0 4px rgba(239, 68, 68, 0.6));
}

/* Enhanced edge labels */
.edge-label {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  padding: 3px 8px;
  font-size: 11px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(4px);
  pointer-events: all;
  transition: all 0.2s ease;
}

.edge-label:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Component palette drag styles */
.component-palette-item {
  cursor: grab;
  transition: all 0.2s;
}

.component-palette-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.component-palette-item:active {
  cursor: grabbing;
  transform: scale(0.95);
}

/* Compact node specific styles */
.react-flow__node {
  cursor: pointer;
}

.react-flow__node:hover {
  z-index: 1000;
}

/* Tooltip styles for compact nodes */
.compact-node-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 8px;
  padding: 6px 12px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  font-size: 12px;
  border-radius: 6px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 1000;
}

.compact-node-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
}

.react-flow__node:hover .compact-node-tooltip {
  opacity: 1;
}

/* Loading states */
.calculating-cost {
  opacity: 0.7;
  pointer-events: none;
}

.calculating-cost::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
