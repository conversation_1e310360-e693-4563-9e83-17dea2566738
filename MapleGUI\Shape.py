'''Class for all non-model objects in diagram.
Functions->
mousePressEvent: makes shape selected in diagram
mouseDoubleClickEvent: does nothing but necessary for parent call
contextMenuEvent: displays right-click menu
addTfr: adds transformer details of selected transformer to shape
displayDetails: displays all shape attributes
del: deletes all connected arrows
addInLink: adds incoming arrow
addOutLink: adds outgoing arrow
itemChange: sends signal for updating position of arrows
pos1: returns centre coordinate of shape in diagram
getList: returns list of attributes for saving
'''


import sys
from PyQt5.QtGui import QPixmap, QPen, QBrush, QColor
from PyQt5.QtWidgets import * #QPushButton, QApplication, QGraphicsEllipseItem, QGraphicsPixmapItem, QGraphicsTextItem, QMenu, QMessageBox, QVBoxLayout, QLabel, QLineEdit
from PyQt5.QtWidgets import QPushButton, QApplication, QGraphicsEllipseItem, QGraphicsPixmapItem, QGraphicsTextItem, QMenu, QMessageBox, QVBoxLayout, QLabel, QLineEdit,QGraphicsProxyWidget,QCheckBox
from Arrow import Arrow
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from ragguru import suggest_rag_architecture
from PyQt5.QtCore import QPointF, QSize, Qt
from PyQt5 import QtGui
from PyQt5.QtGui import QFont
from PyQt5.QtGui import QPixmap
import time
import os
import subprocess
import json
from globalslist import instanceTypeToAcceleratorMap,awslist, gcplist, azurelist, awsworksheet, gcpworksheet, azureworksheet, nextServiceMap,serviceNameToCodeMapping,serviceCodeToParameterMapping,serviceCodeToDropdownMapping#, diagram
import importlib
from lambda_makespan_cost import compute_lambda_latency_and_cost
from s3_makespan_cost import compute_s3_latency_and_cost
from dynamodb_makespan_cost import compute_dynamodb_latency_and_cost
from models import computeCostAndPerf
from check4 import compute_latency,accelerator_params
class Shape(QGraphicsEllipseItem): #All objects in diagram which are not models
    def __init__(self,text,a,b,cost,latency,excelrownum):
        super().__init__(0,0,a,b)
        self.in_arrows = []
        self.out_arrows = []
        self.setBrush(Qt.white)
        self.setPos(a,b)
        self.setAcceptHoverEvents(True)
        self.setFlag(self.ItemIsSelectable)
        self.setFlag(self.ItemIsMovable)
        self.setFlag(self.ItemSendsGeometryChanges)
        self.setFlag(self.ItemIsFocusable)  # Allow the item to receive focus for key events
        self.text = text
        self.a = a
        self.b = b
        self.selectedTime = None
        self.tfrs = []
        self.RowNum = excelrownum
        self.name = awsworksheet[f"A{self.RowNum}"].value
        self.workLoad = None
        self.textItem=None
        self.serviceNameText = None  # Will hold the service name text as a child item
        self.region="ap-south-1"
        self.checkbox = QCheckBox()
        self.checkbox.setChecked(False)
        self.checkbox.setVisible(True)  # checkbox itself is visible

        self.proxy = QGraphicsProxyWidget()
        self.proxy.setWidget(self.checkbox)
        #proxy.setPos(50, 40 * i + 20)
        self.proxy.setVisible(False)  # initially hidden
        self.proxy.setFlag(QGraphicsProxyWidget.ItemIsSelectable,True)

        self.proxy.setFlag(QGraphicsProxyWidget.ItemIsMovable,True)
        #self.addItem(self.proxy)
        '''
        self.Cost = awsworksheet[f"B{self.RowNum}"].value
        self.CostDesc = awsworksheet[f"C{self.RowNum}"].value
        self.Latency = awsworksheet[f"D{self.RowNum}"].value
        self.LatencyDesc = awsworksheet[f"E{self.RowNum}"].value
        self.group = awsworksheet[f"H{self.RowNum}"].value
        self.CloudLatency = 0.0
        self.paramList = awsworksheet[f"F{self.RowNum}"].value
        #self.filePath = './icons/cost-jsons/'
        #self.fileName = serviceNameToCodeMapping[self.name]+'.json'
        '''
        #print("Before num -- "+str(self.RowNum))
        #print("Before  -- "+self.name)
        if (self.name == "User") or (self.name == "LLM") or (self.name == "VectorDB"):
            self.attributes = ""
        elif self.name is not None:
            self.filePath = './icons/cost-jsons/'
            print(self.name)
            serviceCode = serviceNameToCodeMapping[self.name.strip()]
            self.fileName = serviceCode+'.json'
            self.perfAttributes={}
            if serviceCode in serviceCodeToParameterMapping.keys():
                self.attributes = serviceCodeToParameterMapping[serviceCode]
            else:
                try:
                    self.attributes = self.getAtrributesFromJson(self.filePath+self.fileName)
                except:
                    self.attributes = "" #Only AWS has JSONs to handle GCP, AZURE, This is temporary solution

        if(text == "S"):
            self.textItem = QGraphicsPixmapItem(QPixmap("storage.png").scaled(QSize(20,20)),self) #named as textItem to simplify saving
            rect = self.textItem.boundingRect()
            rect.moveCenter(self.boundingRect().center())
            self.textItem.setPos(rect.topLeft())
            self.proxy.setPos(rect.topLeft() - QPointF(5, 0))
            # Set tooltip for the icon to match the service
            tooltip_text = f"{self.name}\nCost: {self.Cost} {self.CostDesc}\nLatency: {self.Latency} {self.LatencyDesc}"
            self.textItem.setToolTip(tooltip_text)
            self.setPen(Qt.white)
        elif(text == "GCP"):
            self.name = gcpworksheet[f"A{self.RowNum}"].value
            self.Cost = gcpworksheet[f"B{self.RowNum}"].value
            self.CostDesc = gcpworksheet[f"C{self.RowNum}"].value
            self.Latency = gcpworksheet[f"D{self.RowNum}"].value
            self.LatencyDesc = gcpworksheet[f"E{self.RowNum}"].value
            self.group = gcpworksheet[f"H{self.RowNum}"].value
            self.CloudLatency = 0.0
            self.paramList = gcpworksheet[f"F{self.RowNum}"].value
            self.compareList = gcpworksheet[f"I{self.RowNum}"].value
            servicename = gcpworksheet[f"A{self.RowNum}"].value
            icon_filename = gcpworksheet[f"A{self.RowNum}"].value+".png"
            if icon_filename in os.listdir('icons/gcp'):
                self.textItem = QGraphicsPixmapItem(QPixmap("icons/gcp/"+icon_filename).scaled(QSize(60,60)),self)
            else:
                self.textItem = QGraphicsPixmapItem(QPixmap("maple.png").scaled(QSize(60,60)),self)
            # Set tooltip for the icon to match the service
            tooltip_text = f"{self.name}\nCost: {self.Cost} {self.CostDesc}\nLatency: {self.Latency} {self.LatencyDesc}"
            self.textItem.setToolTip(tooltip_text)
            rect = self.textItem.boundingRect()
            rect.moveCenter(self.boundingRect().center())
            self.textItem.setPos(rect.topLeft())
            self.proxy.setPos(rect.topLeft() - QPointF(10, 0))
            self.setPen(QPen(Qt.NoPen))#QColor(58,58,58))
            self.setBrush(QBrush(Qt.NoBrush))#QColor(58,58,58))
        elif(text == "AZR"):
            self.name = azureworksheet[f"A{self.RowNum}"].value
            self.Cost = azureworksheet[f"B{self.RowNum}"].value
            self.CostDesc = azureworksheet[f"C{self.RowNum}"].value
            self.Latency = azureworksheet[f"D{self.RowNum}"].value
            self.LatencyDesc = azureworksheet[f"E{self.RowNum}"].value
            self.group = azureworksheet[f"H{self.RowNum}"].value
            self.CloudLatency = 0.0
            self.paramList = azureworksheet[f"F{self.RowNum}"].value
            self.compareList = azureworksheet[f"I{self.RowNum}"].value

            servicename = azureworksheet[f"A{self.RowNum}"].value
            icon_filename = azureworksheet[f"A{self.RowNum}"].value+".png"
            if icon_filename in os.listdir('icons/azure'):
                self.textItem = QGraphicsPixmapItem(QPixmap("icons/azure/"+icon_filename).scaled(QSize(60,60)),self)
            else:
                self.textItem = QGraphicsPixmapItem(QPixmap("maple.png").scaled(QSize(60,60)),self)
            # Set tooltip for the icon to match the service
            tooltip_text = f"{self.name}\nCost: {self.Cost} {self.CostDesc}\nLatency: {self.Latency} {self.LatencyDesc}"
            self.textItem.setToolTip(tooltip_text)
            rect = self.textItem.boundingRect()
            rect.moveCenter(self.boundingRect().center())
            self.textItem.setPos(rect.topLeft())
            self.proxy.setPos(rect.topLeft() - QPointF(5, 0))
            self.setPen(QPen(Qt.NoPen))#QColor(58,58,58))
            self.setBrush(QBrush(Qt.NoBrush))#QColor(58,58,58))
        elif(text == "CSP"):
            self.name = awsworksheet[f"A{self.RowNum}"].value
            self.Cost = awsworksheet[f"B{self.RowNum}"].value
            self.CostDesc = awsworksheet[f"C{self.RowNum}"].value
            self.Latency = awsworksheet[f"D{self.RowNum}"].value
            self.LatencyDesc = awsworksheet[f"E{self.RowNum}"].value
            self.group = awsworksheet[f"H{self.RowNum}"].value
            self.CloudLatency = 0.0
            self.paramList = awsworksheet[f"F{self.RowNum}"].value
            self.compareList = awsworksheet[f"I{self.RowNum}"].value

            servicename = awsworksheet[f"A{self.RowNum}"].value
            print("rownummmmmmmmmmmmmmmmmmmmmmmmmmm")
            print(self.RowNum)
            icon_filename = awsworksheet[f"A{self.RowNum}"].value+".png"
            if icon_filename in os.listdir('icons/aws'):
                self.textItem = QGraphicsPixmapItem(QPixmap("icons/aws/"+icon_filename).scaled(QSize(60,60)),self)
            else:
                self.textItem = QGraphicsPixmapItem(QPixmap("maple.png").scaled(QSize(60,60)),self)
            # Set tooltip for the icon to match the service
            tooltip_text = f"{self.name}\nCost: {self.Cost} {self.CostDesc}\nLatency: {self.Latency} {self.LatencyDesc}"
            self.textItem.setToolTip(tooltip_text)
            rect = self.textItem.boundingRect()
            rect.moveCenter(self.boundingRect().center())
            self.textItem.setPos(rect.topLeft())
            self.proxy.setPos(rect.topLeft() - QPointF(5, 0))
            self.setPen(QPen(Qt.NoPen))#QColor(58,58,58))
            self.setBrush(QBrush(Qt.NoBrush))#QColor(58,58,58))
        else:
            self.textItem = QGraphicsTextItem(self)
            self.textItem.setHtml('<center><b><p style="font-size:25px;">' + "S3" + '</p></b></center>')
            self.textItem.setTextWidth(self.boundingRect().width())
            rect = self.textItem.boundingRect()
            rect.moveCenter(self.boundingRect().center())
            self.textItem.setPos(rect.topLeft())
            self.proxy.setPos(rect.topLeft() - QPointF(5, 0))

    def createServiceNameText(self, serviceName):
        """Create service name text as a child item that moves with the shape"""
        if self.serviceNameText is not None:
            # Remove existing text if it exists
            self.serviceNameText.setParentItem(None)

        # Create new service name text as a child of this shape
        self.serviceNameText = QGraphicsTextItem(self)
        self.serviceNameText.setPlainText(serviceName.replace("Amazon ", "").replace("AWS ", ""))
        self.serviceNameText.setDefaultTextColor(QColor(Qt.black))

        # Set font
        font = QFont()
        font.setPointSize(15)
        self.serviceNameText.setFont(font)

        # Position the text below the service icon (relative to parent Shape)
        self.serviceNameText.setPos(QPointF(0, 55))

        # Make it selectable and movable (but it will move with parent)
        self.serviceNameText.setFlag(QGraphicsTextItem.ItemIsSelectable, True)
        self.serviceNameText.setFlag(QGraphicsTextItem.ItemIsMovable, True)

        # For backward compatibility, also set textItem to point to the service name text
        self.textItem = self.serviceNameText
        return self.serviceNameText

    def deleteService(self):
        """Properly delete this service and all its components"""
        # Get the diagram instance
        cswid = importlib.import_module("CSWidgets")
        if hasattr(cswid, 'diagram') and cswid.diagram is not None:
            # Remove from scene - this will automatically remove child items
            cswid.diagram.scene.removeItem(self)

            # Remove proxy if it exists
            if hasattr(self, 'proxy') and self.proxy is not None:
                cswid.diagram.scene.removeItem(self.proxy)

            # Clean up arrows
            for arrow in self.in_arrows[:]:  # Use slice copy to avoid modification during iteration
                cswid.diagram.scene.removeItem(arrow)
                self.in_arrows.remove(arrow)

            for arrow in self.out_arrows[:]:  # Use slice copy to avoid modification during iteration
                cswid.diagram.scene.removeItem(arrow)
                self.out_arrows.remove(arrow)

    # mouse hover event
    def hoverEnterEvent(self, event):
        # Create enhanced tooltip with cost and latency information
        tooltip_text = f"""
        <div style='background-color: #ffffff; padding: 10px; border: 1px solid #cccccc; border-radius: 5px; min-width: 250px;'>
            <h3 style='color: #007bff; margin: 0 0 10px 0;'>{self.name}</h3>
            <p style='margin: 5px 0;'><b>Cost:</b> {self.Cost} {self.CostDesc}</p>
            <p style='margin: 5px 0;'><b>Latency:</b> {self.Latency} {self.LatencyDesc}</p>
        </div>
        """
        self.setToolTip(tooltip_text)

        # Also set tooltip for the textItem (icon) if it's a child item
        if hasattr(self, 'textItem') and self.textItem is not None:
            self.textItem.setToolTip(tooltip_text)

        # Set tooltip for the proxy (checkbox) as well
        if hasattr(self, 'proxy') and self.proxy is not None:
            self.proxy.setToolTip(tooltip_text)

        super().hoverEnterEvent(event)

    def hoverLeaveEvent(self, event):
        super().hoverLeaveEvent(event)

    def __reduce__(self):
        return (self.__class__, (self.text, self.a, self.b, self.Cost, self.Latency, self.RowNum))

    def createProxy(self):
        """Create a proxy widget for the checkbox if it doesn't exist"""
        if not hasattr(self, 'proxy') or self.proxy is None:
            self.proxy = QGraphicsProxyWidget()
            if hasattr(self, 'checkbox') and self.checkbox is not None:
                self.proxy.setWidget(self.checkbox)
            self.proxy.setFlag(QGraphicsProxyWidget.ItemIsSelectable, True)
            self.proxy.setFlag(QGraphicsProxyWidget.ItemIsMovable, True)
            self.proxy.setVisible(False)  # initially hidden

    def createCheckbox(self):
        """Create a checkbox if it doesn't exist"""
        if not hasattr(self, 'checkbox') or self.checkbox is None:
            self.checkbox = QCheckBox()
            self.checkbox.setChecked(False)
            self.checkbox.setVisible(True)  # checkbox itself is visible

            # If proxy exists, set the checkbox as its widget
            if hasattr(self, 'proxy') and self.proxy is not None:
                self.proxy.setWidget(self.checkbox)

    # mouse click event
    def mousePressEvent(self, event):
        self.setSelected(True)
        self.setFocus()  # Set focus so this item can receive key events

        # Select the textItem (icon or text)
        if hasattr(self, 'textItem') and self.textItem is not None:
            self.textItem.setSelected(True)

        # Select the service name text
        if hasattr(self, 'serviceNameText') and self.serviceNameText is not None:
            self.serviceNameText.setSelected(True)

        # Select the proxy (checkbox)
        if hasattr(self, 'proxy') and self.proxy is not None:
            self.proxy.setSelected(True)

        super().mousePressEvent(event)

    def keyPressEvent(self, event):
        """Handle key press events for deletion"""
        if event.key() == Qt.Key_Delete or event.key() == Qt.Key_Backspace:
            # Delete this service when Delete or Backspace is pressed
            self.deleteService()
            event.accept()
        else:
            super().keyPressEvent(event)

    def mouseDoubleClickEvent(self, event):
        # Set this shape as selected
        self.setSelected(True)

        # Get the diagram instance
        cswid = importlib.import_module("CSWidgets")
        diagram = cswid.diagram

        # If there's already a selected service, this is the second service
        # Connect them with an arrow
        if diagram.connectorActive and len(diagram.scene.selectedItems()) == 2:
            # Call the connector functionality directly
            diagram.addArrow(None)
        else:
            # Just select this service normally
            super().mouseDoubleClickEvent(event)

    # Handle mouse release event for connector functionality
    def mouseReleaseEvent(self, event):
        # Call the parent class implementation first
        super().mouseReleaseEvent(event)

        # Get the diagram instance
        cswid = importlib.import_module("CSWidgets")
        diagram = cswid.diagram

        # Only process if connector mode is active
        if diagram.connectorActive:
            # If this is the first service selected in connector mode
            if diagram.firstSelectedService is None:
                diagram.firstSelectedService = self
                # Provide visual feedback that the first service is selected
                self.setSelected(True)
                cswid.labelmarquee.setText(" :: First service selected. Now click on the second service to connect them.")
                print(f"First service selected: {self.name}")
            else:
                # This is the second service, create the connection
                start_item = diagram.firstSelectedService
                end_item = self

                # Don't connect a service to itself
                if start_item == end_item:
                    cswid.labelmarquee.setText(" :: Cannot connect a service to itself. Please select a different service.")
                    diagram.firstSelectedService = None  # Reset selection
                    self.setSelected(False)
                    return

                try:
                    print(f"Creating arrow from {start_item.name} to {end_item.name}")

                    # Create the arrow
                    arrow = Arrow(start_item, end_item, True)

                    # Add the arrow to the scene
                    diagram.scene.addItem(arrow)

                    # Add the arrow's proxy widget for the checkbox
                    diagram.scene.addItem(arrow.proxy)

                    # Add to adjacency matrix
                    diagram.adjacency_matrix.append([start_item, end_item, arrow])

                    # Reset selection state but stay in connector mode
                    start_item.setSelected(False)
                    self.setSelected(False)
                    diagram.firstSelectedService = None

                    # Update the UI to indicate the connection was made
                    cswid.labelmarquee.setText(" :: Connection created. You can create another connection or click 'Connector' to exit connector mode.")

                    # Print debug information
                    print(f"Arrow created from {start_item.name} to {end_item.name}")
                except Exception as e:
                    print(f"Error creating arrow: {str(e)}")
                    cswid.labelmarquee.setText(" :: Error creating connection. Please try again.")
                    # Reset selection state on error
                    start_item.setSelected(False)
                    self.setSelected(False)
                    diagram.firstSelectedService = None

    #app = QApplication(sys.argv)



    def contextMenuEvent(self, event): #show on right click. Add condition to show menu only on right click. Event filter?
        if(self.text == "GCP" or self.text == "AZR" or self.text == "CSP"):# Input & Output, dont need right click menus
            menu = QMenu()
            menuDefcostperf =  menu.addAction("Current Configuration")
            menuDefcostperf.triggered.connect(self.defaultCostPerf)
            menuCustcostperf = menu.addAction("Custom Cost && Performance")
            menuCustcostperf.triggered.connect(self.CustCostPerf)
            perfact = QMenu("Show Performance")
            costact = QMenu("Show Cost")
            perfdefaultact = perfact.addAction("Default Performance")
            perfdefaultact.triggered.connect(self.displayDefaultPerf)
            perfcustomact = perfact.addAction("Custom Performance")
            perfcustomact.triggered.connect(self.displayCustPerf)
            costdefaultfact = costact.addAction("Default Cost")
            costdefaultfact.triggered.connect(self.displayDefaultCost)
            costcustomact = costact.addAction("Custom Cost")
            costcustomact.triggered.connect(self.displayCustCost)
            #menu.addMenu(perfact)
            #menu.addMenu(costact)
            # Removed the last 4 options as requested:
            # 1. "Input and Output"
            # 2. "Next Suggested"
            # 3. "Compare"
            # 4. "Ask AI"
            menu.exec(event.screenPos())
        elif((self.text != "I") & (self.text != "O")):# Input & Output, dont need right click menus
            menu = QMenu()
            detailsaction =  menu.addAction("Show Transformar Details")
            detailsaction.triggered.connect(self.displayDetails)
            tfrsub = QMenu("Select Transformer")
            for tfr in self.tfrs:
                #serveraction = tfrsub.addAction("Transformer" + str(tfr.ID))
                serveraction = tfrsub.addAction(str(tfr.Type))
                serveraction.triggered.connect(lambda chk, tfr=tfr: self.addTfr(tfr))
            menu.addMenu(tfrsub)
            menu.exec(event.screenPos())

    def func(self):
        sender_action = self.sender()
        print(sender_action.text())

    def defaultCostPerf(self):
        msg = QMessageBox()
        msg.setWindowTitle("Current Config")
        from Diagram import GraphicView

        # Define the top 5 most relevant configuration attributes to display
        top_attributes = []

        # If attributes exist, select the top 5 most relevant ones
        if hasattr(self, 'attributes') and self.attributes:
            # Priority order for relevant attributes
            priority_keys = [
                'instanceType',
                'location',
                'vCPU',
                'memory',
                'storage',
                'operatingSystem',
                'networkPerformance'
            ]

            # First add the priority keys if they exist
            for key in priority_keys:
                if key in self.attributes and len(top_attributes) < 5:
                    top_attributes.append((key, self.attributes[key]))

            # If we still need more attributes to reach 5, add other available ones
            if len(top_attributes) < 5:
                for key in self.attributes:
                    if key not in [item[0] for item in top_attributes] and len(top_attributes) < 5:
                        top_attributes.append((key, self.attributes[key]))

        # Build the HTML content
        html_content = f"<font color='#ffa500',font size='6'>{self.name}</font><br><br>"

        # Add the top 5 attributes
        if top_attributes:
            for key, value in top_attributes:
                html_content += f"<font color='#ff0000',font size='4'>{key}: </font>{value}<br>"

        # Always add cost and latency information
        html_content += f"<br><font color='#ff0000',font size='4'>Cost: </font>{self.Cost} {self.CostDesc}<br>"
        html_content += f"<font color='#ff0000',font size='4'>Latency: </font>{self.Latency} {self.LatencyDesc}<br>"

        msg.setText(html_content)
        x = msg.exec_()

    def displayIOMenu(self):
        self.formGroupBox = QGroupBox()
        self.formGroupBox.setWindowTitle("Input && Output")
        self.formGroupBox.setWindowIcon(QtGui.QIcon('mapleicon.png'))
        self.layout = QFormLayout()
        self.tabWidget = QTabWidget()
        tab1 = QWidget()
        tab2 = QWidget()
        # tab3 = QWidget()
        params = ["a","b","c"]
        self.tabWidget.addTab(tab1,"Input")
        self.tabWidget.addTab(tab2,"Output")
        self.layout.addRow(self.tabWidget)

        tab1_layout = QFormLayout()
        for val in params:
            self.label = QLabel(val+" ")
            self.text_box = QLineEdit()
            tab1_layout.addRow(self.label,self.text_box)
        update_button = QPushButton("Update")
        apply_button = QPushButton("Apply")
        HBOX  = QHBoxLayout()
        # update_button.clicked.connect(self.displayCustCostSubmit)
        # apply_button.clicked.connect(self.applyAttributes)
        HBOX.addWidget(update_button)
        HBOX.addWidget(apply_button)
        tab1_layout.addRow(HBOX)
        tab1.setLayout(tab1_layout)

        tab2_layout = QFormLayout()
        for val in params:
            self.label = QLabel(val+" ")
            self.text_box = QLineEdit()
            tab2_layout.addRow(self.label,self.text_box)
        # tab2_layout.addRow(QLabel("Storage Label t21"), QLineEdit())
        # tab2_layout.addRow(QLabel("Storage Label t22"), QLineEdit())
        update_button = QPushButton("Update")
        apply_button = QPushButton("Apply")
        HBOX  = QHBoxLayout()
        # update_button.clicked.connect(self.displayCustCostSubmit)
        # apply_button.clicked.connect(self.applyAttributes)
        HBOX.addWidget(update_button)
        HBOX.addWidget(apply_button)
        tab2_layout.addRow(HBOX)
        tab2.setLayout(tab2_layout)

        self.formGroupBox.setLayout(self.layout)
        self.formGroupBox.show()

    def compute_s_metrics(self,batch, input_seq, dim, dim_hidden, vocab, layers, num_head, num_key_val_heads, head_dim, out_new_seq, max_context_length, parameters):
        seq_initial = input_seq

        total_Sall, total_S1, total_S2, total_S3, total_S4, total_KV = 0, 0, 0, 0, 0, 0

        for n in range(1, out_new_seq+1):
            if n > 1:
                input_seq = 1
            else:
                input_seq = seq_initial

            S_WQ_Matmul = (3 * 2 * dim * dim)
            S_Weighted_Output = 2 * dim * dim

            S_W1_Gate_Projection = 2 * dim * dim_hidden
            S_W2_Down_Projection = 2 * dim * dim_hidden
            S_W3_Up_Projection = 2 * dim * dim_hidden

            S1 = (S_WQ_Matmul + S_Weighted_Output) * layers
            S2 = (S_W1_Gate_Projection + S_W2_Down_Projection + S_W3_Up_Projection) * layers
            S3 = vocab * dim * 2
            S4 = vocab * dim * 2
            KV = (seq_initial + out_new_seq - 1) * num_key_val_heads * head_dim * 2 * 2 * batch * layers

            total_Sall = S1 + S2 + S3 + S4
            total_S1 = S1
            total_S2 = S2
            total_S3 = S3
            total_S4 = S4
            total_KV = KV

        return total_Sall, total_S1, total_S2, total_S3, total_S4, total_KV



    def compute_m_metrics(self,batch, input_seq, dim, dim_hidden, vocab, layers, num_head, num_key_val_heads, head_dim, out_new_seq, max_context_length, parameters):
        seq_initial = input_seq
        total_Mall, total_M1, total_M2, total_M3, total_M4, total_M5 = 0, 0, 0, 0, 0, 0

        for n in range(1, out_new_seq + 1):
            if n > 1:
                input_seq = 1
            else:
                input_seq = input_seq

            M_RMS_norm = 2 * batch * input_seq * dim
            M_WQ_Matmul = (2 * batch * input_seq * dim) + (2 * num_head * head_dim * dim)
            M_WKV_Matmul = 2 * 2 * dim * num_key_val_heads * head_dim
            M_QK_Matmul = (2 * batch * input_seq * num_head * head_dim) + 2 * batch * (seq_initial + out_new_seq - 1) * num_key_val_heads * head_dim
            M_Softmax = 2 * batch * input_seq * (seq_initial + out_new_seq - 1)
            M_Score_Matmul_V = (2 * batch * input_seq * (seq_initial + out_new_seq - 1)) + (2 * batch * (seq_initial + out_new_seq - 1) * num_key_val_heads * head_dim)
            M_Weighted_Output = (2 * batch * input_seq * num_head * head_dim) + (2 * num_head * head_dim * dim)

            M_W1_Gate_Projection = (2 * dim * dim_hidden) + (2 * batch * input_seq * dim)
            M_W2_Down_Projection = (2 * dim * dim_hidden) + (2 * batch * input_seq * dim_hidden)
            M_W3_Up_Projection = 2 * dim * dim_hidden + (2 * batch * input_seq * dim)
            M_MLP_Dot_Product = 2 * batch * input_seq * dim_hidden
            M_SiLU_Activation = 2 * batch * input_seq * dim_hidden

            M1 = (M_RMS_norm + M_WQ_Matmul + M_WKV_Matmul + M_QK_Matmul + M_Softmax + M_Score_Matmul_V + M_Weighted_Output) * layers
            M2 = (M_RMS_norm + M_W1_Gate_Projection + M_W2_Down_Projection + M_W3_Up_Projection + M_MLP_Dot_Product + M_SiLU_Activation) * layers
            M3 = (vocab * dim * 2) + (2 * batch * input_seq * dim)
            M4 = vocab * dim * 2
            M5 = 2 * batch * vocab

            total_Mall += M1 + M2 + M3 + M4 + M5
            total_M1 += M1
            total_M2 += M2
            total_M3 += M3
            total_M4 += M4
            total_M5 += M5

        return total_Mall, total_M1, total_M2, total_M3, total_M4, total_M5



    def compute_f_metrics(self,batch, input_seq, dim, dim_hidden, vocab, layers, num_head, num_key_val_heads, head_dim, out_new_seq, max_context_length, parameters):
        seq_initial = input_seq
        total_Fall, total_F1, total_F2, total_F3, total_F5 = 0, 0, 0, 0, 0

        for n in range(1, out_new_seq + 1):
            if n > 1:
                input_seq = 1
            else:
                input_seq = seq_initial

            F_RMS_norm = batch * input_seq * (dim + dim + 1)
            F_WQ_Matmul = 2 * batch * input_seq * dim * num_head * head_dim
            F_WKV_Matmul = 2 * 2 * batch * input_seq * dim * num_key_val_heads * head_dim
            F_QK_Matmul = 2 * batch * input_seq * num_head * head_dim * (seq_initial + out_new_seq - 1)
            F_Softmax = batch * input_seq * (3 * (seq_initial + out_new_seq - 1))
            F_Score_Matmul_V = 2 * batch * input_seq * (num_key_val_heads * head_dim) * (seq_initial + out_new_seq - 1)
            F_Weighted_Output = 2 * batch * input_seq * num_head * head_dim

            F_W1_Gate_Projection = 2 * batch * input_seq * dim * dim_hidden
            F_W2_Down_Projection = 2 * batch * input_seq * dim * dim_hidden
            F_W3_Up_Projection = 2 * batch * input_seq * dim * dim_hidden
            F_MLP_Dot_Product = batch * input_seq * dim_hidden
            F_SiLU_Activation = 2 * batch * input_seq * dim_hidden

            F1 = (F_RMS_norm + F_WQ_Matmul + F_WKV_Matmul + F_QK_Matmul + F_Softmax + F_Score_Matmul_V + F_Weighted_Output) * layers
            F2 = (F_RMS_norm + F_W1_Gate_Projection + F_W2_Down_Projection + F_W3_Up_Projection + F_MLP_Dot_Product + F_SiLU_Activation) * layers
            F3 = 2 * batch * input_seq * dim * vocab
            F5 = batch * (4 * vocab)

            total_Fall += F1 + F2 + F3 + F5
            total_F1 += F1
            total_F2 += F2
            total_F3 += F3
            total_F5 += F5

        return total_Fall, total_F1, total_F2, total_F3, total_F5

    def carllm(self):
        model_params = {
    "LLaMA2_7B": {
        "m_ind": 3,
        "actual_name": "LLaMA 2-7B",
        "dim": 4096,
        "dim_hidden": 11008,
        "vocab": 32000,
        "layers": 32,
        "num_head": 32,
        "num_key_val_heads": 32,
        "head_dim": 128,
        "max_context_length": 4096,
        "parameters": 7000000000
    },
    "LLaMA2_13B": {
        "m_ind": 1,
        "actual_name": "LLaMA 2-13B",
        "dim": 5120,
        "dim_hidden": 13824,
        "vocab": 32000,
        "layers": 40,
        "num_head": 40,
        "num_key_val_heads": 40,
        "head_dim": 128,
        "max_context_length": 4096,
        "parameters": 13000000000
    },
    "LLaMA2_70B": {
        "m_ind": 2,
        "actual_name": "LLaMA 2-70B",
        "dim": 8192,
        "dim_hidden": 28672,
        "vocab": 32000,
        "layers": 80,
        "num_head": 64,
        "num_key_val_heads": 8,
        "head_dim": 128,
        "max_context_length": 4096,
        "parameters": 70000000000
    },
    "Mistral_7B": {
        "m_ind": 5,
        "actual_name": "Mistral-7B",
        "dim": 4096,
        "dim_hidden": 14336,
        "vocab": 32000,
        "layers": 32,
        "num_head": 32,
        "num_key_val_heads": 8,
        "head_dim": 128,
        "max_context_length": 131072,
        "parameters": 7000000000
    },
    "LLaMA3_8B" : {
        "m_ind": 4,
        "actual_name": "LLaMA 3-8B",
        "dim": 4096,
        "dim_hidden": 14336,
        "vocab": 128256,
        "layers": 32 ,
        "num_head": 32 ,
        "num_key_val_heads": 8 ,
        "head_dim": 128,
        "max_context_length": 8192,
        "parameters": 8000000000
    },
    "GPTJ_6B"   : {
        "m_ind": 0,
        "actual_name": "GPTJ-6B",
        "dim": 4096,
        "dim_hidden": 16384,
        "vocab": 50400,
        "layers": 28,
        "num_head": 16,
        "num_key_val_heads": 16,
        "head_dim": 256,
        "max_context_length": 4096,
        "parameters": 6000000000
    }
}





        accelerator_params = {
  "A100(GCP)": {
      "LLaMA2_7B" :{"a_ind":0,"dp":1,"max_chips": 1, "compute_bw": 312000000000000, "memory_bw": 1555e9, "Inter_comm": 300e9, "max_batch": 64, "max_storage": 40e9, "cost": 3.67,"cloud": "GCP", "instance":"a2-highgpu-1g"},
      "LLaMA2_13B":{"a_ind":0,"dp":1,"max_chips": 1, "compute_bw": 312000000000000, "memory_bw": 1555e9, "Inter_comm": 300e9, "max_batch": 64, "max_storage": 40e9, "cost": 3.67,"cloud": "GCP", "instance":"a2-highgpu-1g"},
      "LLaMA2_70B":{"a_ind":0,"dp":1,"max_chips": 2, "compute_bw": 312000000000000, "memory_bw": 1555e9, "Inter_comm": 300e9, "max_batch": 64, "max_storage": 160e9, "cost": 14.7,"cloud": "GCP", "instance":"a2-highgpu-4g"},
      "Mistral_7B":{"a_ind":0,"dp":1,"max_chips": 1, "compute_bw": 312000000000000, "memory_bw": 1555e9, "Inter_comm": 300e9, "max_batch": 64, "max_storage": 40e9, "cost": 3.67,"cloud": "GCP", "instance":"a2-highgpu-1g"},
      "LLaMA3_8B" :{"a_ind":0,"dp":1,"max_chips": 1, "compute_bw": 312000000000000, "memory_bw": 1555e9, "Inter_comm": 300e9, "max_batch": 64, "max_storage": 40e9, "cost": 3.67,"cloud": "GCP", "instance":"a2-highgpu-1g"},
      "GPTJ_6B"   :{"a_ind":0,"dp":1,"max_chips": 1, "compute_bw": 312000000000000, "memory_bw": 1555e9, "Inter_comm": 300e9, "max_batch": 64, "max_storage": 40e9, "cost": 3.67,"cloud": "GCP", "instance":"a2-highgpu-1g"}
  },
  "A100(AWS)": {
      "LLaMA2_7B":{"a_ind":0,"dp":1,"max_chips": 8, "compute_bw": 312000000000000, "memory_bw": 1555e9, "Inter_comm": 300e9, "max_batch": 64, "max_storage": 40e9, "cost": 32.77,"cloud": "AWS", "instance":"p4d.24xlarge"},
      "LLaMA2_13B":{"a_ind":0,"dp":1,"max_chips": 8, "compute_bw": 312000000000000, "memory_bw": 1555e9, "Inter_comm": 300e9, "max_batch": 64, "max_storage": 40e9, "cost": 32.77,"cloud": "AWS", "instance":"p4d.24xlarge"},
      "LLaMA2_70B":{"a_ind":0,"dp":1,"max_chips": 8, "compute_bw": 312000000000000, "memory_bw": 1555e9, "Inter_comm": 300e9, "max_batch": 64, "max_storage": 160e9, "cost": 32.77,"cloud": "AWS", "instance":"p4d.24xlarge"},
      "Mistral_7B":{"a_ind":0,"dp":1,"max_chips": 8, "compute_bw": 312000000000000, "memory_bw": 1555e9, "Inter_comm": 300e9, "max_batch": 64, "max_storage": 40e9, "cost": 32.77,"cloud": "AWS", "instance":"p4d.24xlarge"},
      "LLaMA3_8B":{"a_ind":0,"dp":1,"max_chips": 8, "compute_bw": 312000000000000, "memory_bw": 1555e9, "Inter_comm": 300e9, "max_batch": 64, "max_storage": 40e9, "cost": 32.77,"cloud": "AWS", "instance":"p4d.24xlarge"},
      "GPTJ_6B":{"a_ind":0,"dp":1,"max_chips": 8, "compute_bw": 312000000000000, "memory_bw": 1555e9, "Inter_comm": 300e9, "max_batch": 64, "max_storage": 40e9, "cost": 32.77,"cloud": "AWS", "instance":"p4d.24xlarge"}
  },
  "V100(AWS)":      {
      "LLaMA2_7B":{"a_ind":5,"dp":1,"max_chips": 1, "compute_bw": 28260000000000, "memory_bw": 897000000000, "Inter_comm": 15e9, "max_batch": 64, "max_storage": 16e9, "cost": 3.06,"cloud": "AWS","instance":"p3.2xlarge"},
      "LLaMA2_13B":{"a_ind":5,"dp":1,"max_chips": 4, "compute_bw": 28260000000000, "memory_bw": 897000000000, "Inter_comm": 15e9, "max_batch": 64, "max_storage": 64e9, "cost": 12.24,"cloud": "AWS","instance":"p3.8xlarge"},
      "Mistral_7B":{"a_ind":5,"dp":1,"max_chips": 1, "compute_bw": 28260000000000, "memory_bw": 897000000000, "Inter_comm": 15e9, "max_batch": 64, "max_storage": 16e9, "cost": 3.06,"cloud": "AWS","instance":"p3.2xlarge"}
  },
  "V100(GCP)":      {
      "LLaMA2_7B":{"a_ind":5,"dp":1,"max_chips": 1, "compute_bw": 28260000000000, "memory_bw": 897000000000, "Inter_comm": 15e9, "max_batch": 64, "max_storage": 16e9, "cost": 1.77,"cloud": "GCP","instance":"n1-standard-1"},
      "LLaMA2_13B":{"a_ind":5,"dp":1,"max_chips": 2, "compute_bw": 28260000000000, "memory_bw": 897000000000, "Inter_comm": 15e9, "max_batch": 64, "max_storage": 32e9, "cost": 3.51,"cloud": "GCP","instance":"n1-standard-2"},
      "Mistral_7B":{"a_ind":5,"dp":1,"max_chips": 1, "compute_bw": 28260000000000, "memory_bw": 897000000000, "Inter_comm": 15e9, "max_batch": 64, "max_storage": 16e9, "cost": 1.77,"cloud": "GCP","instance":"n1-standard-1"}
  },
  "Gaudi":     {
      "LLaMA2_7B":{"a_ind":1,"dp":1,"max_chips": 8, "compute_bw": 131000000000000, "memory_bw": 1000000000000, "Inter_comm": 12.5e9, "max_batch": 64, "max_storage": 256e9, "cost": 13.11,"cloud": "AWS","instance":"dl1.24xLarge"},
      "LLaMA2_13B":{"a_ind":1,"dp":1,"max_chips": 8, "compute_bw": 131000000000000, "memory_bw": 1000000000000, "Inter_comm": 12.5e9, "max_batch": 64, "max_storage": 256e9, "cost": 13.11,"cloud": "AWS","instance":"dl1.24xLarge"},
      "LLaMA2_70B":{"a_ind":1,"dp":1,"max_chips": 8, "compute_bw": 131000000000000, "memory_bw": 1000000000000, "Inter_comm": 12.5e9, "max_batch": 64, "max_storage": 256e9, "cost": 13.11,"cloud": "AWS","instance":"dl1.24xLarge"},
      "Mistral_7B":{"a_ind":1,"dp":1,"max_chips": 8, "compute_bw": 131000000000000, "memory_bw": 1000000000000, "Inter_comm": 12.5e9, "max_batch": 64, "max_storage": 256e9, "cost": 13.11,"cloud": "AWS","instance":"dl1.24xLarge"}
  },
  "TPU":      {
      "LLaMA2_7B":{"a_ind":4,"dp":1,"max_chips": 4, "compute_bw": 123000000000000, "memory_bw": 900000000000, "Inter_comm": 200e9, "max_batch": 32, "max_storage": 128e9, "cost": 8.00,"cloud": "GCP", "instance":"TPU v3x8"},
      "LLaMA2_13B":{"a_ind":4,"dp":1,"max_chips": 4, "compute_bw": 123000000000000, "memory_bw": 900000000000, "Inter_comm": 200e9, "max_batch": 32, "max_storage": 128e9, "cost": 8.00,"cloud": "GCP", "instance":"TPU v3x8"}
  },
 "Inferentia":{
      "LLaMA2_7B":{"a_ind":3,"dp":1,"max_chips": 12, "compute_bw": 1.9e14, "memory_bw": 820000000000, "Inter_comm": 192e9, "max_batch": 4, "max_storage": 64e9, "cost": 12.98,"cloud": "AWS", "instance":"Inf2.48xlarge"},
      "LLaMA2_13B":{"a_ind":3,"dp":1,"max_chips": 12, "compute_bw": 1.9e14, "memory_bw": 820000000000, "Inter_comm": 192e9, "max_batch": 4, "max_storage": 64e9, "cost": 12.98,"cloud": "AWS", "instance":"Inf2.48xlarge"},
      "LLaMA2_70B":{"a_ind":3,"dp":1,"max_chips": 12, "compute_bw": 1.9e14, "memory_bw": 820000000000, "Inter_comm": 192e9, "max_batch": 4, "max_storage": 64e9, "cost": 12.98,"cloud": "AWS", "instance":"Inf2.48xlarge"},
      "Mistral_7B":{"a_ind":3,"dp":1,"max_chips": 12, "compute_bw": 1.9e14, "memory_bw": 820000000000, "Inter_comm": 192e9, "max_batch": 4, "max_storage": 64e9, "cost": 12.98,"cloud": "AWS", "instance":"Inf2.48xlarge"},
      "LLaMA3_8B":{"a_ind":3,"dp":1,"max_chips": 12, "compute_bw": 1.9e14, "memory_bw": 820000000000, "Inter_comm": 192e9, "max_batch": 4, "max_storage": 64e9, "cost": 12.98,"cloud": "AWS", "instance":"Inf2.48xlarge"}

  },
  "H100":      {
      "LLaMA3_8B":{"a_ind":2,"dp":1,"max_chips": 1, "compute_bw": 1979e12, "memory_bw": 3.35e12, "Inter_comm": 900e9, "max_batch": 64, "max_storage": 80e9, "cost": 11.06,"cloud": "GCP", "instance":"a3-highgpu-1g"},
      "Mistral_7B":{"a_ind":2,"dp":1,"max_chips": 1, "compute_bw": 1979e12, "memory_bw": 3.35e12, "Inter_comm": 900e9, "max_batch": 64, "max_storage": 80e9, "cost": 11.06,"cloud": "GCP", "instance":"a3-highgpu-1g"}
  }
}
        self.formGroupBox1ash.close()
        dicti={}
        import numpy as np
        for i in range(self.layout1.count()):
          item = self.layout1.itemAt(i)
          if item and isinstance(item.widget(),QLineEdit):
            val = item.widget().text()
            key = self.layout1.labelForField(item.widget()).text()
            dicti[key]=val
          elif item and isinstance(item.widget(),QComboBox):
            key = self.layout1.labelForField(item.widget()).text()
            val = item.widget().currentText()
            dicti[key]=val
        models = {
    "Text Generation": ['LLaMA2_70B', 'LLaMA3_8B','LLaMA2_13B', 'Mistral_7B', 'LLaMA2_7B','GPTJ_6B'],
    "Summarization": ['Mistral_7B','LLaMA3_8B','LLaMA2_7B', 'LLaMA2_13B', 'LLaMA2_70B', 'GPTJ_6B'],
    "Q&A": ['Mistral_7B','LLaMA3_8B', 'LLaMA2_7B', 'LLaMA2_13B', 'LLaMA2_70B','GPTJ_6B'],
    "Bi-lingual Translation": ['Mistral_7B','LLaMA3_8B', 'LLaMA2_7B', 'LLaMA2_13B', 'LLaMA2_70B','GPTJ_6B']
}
        top_k=-1
        for k,v in dicti.items():
            if "Application type:" in k:
                modellist=models[v]
            if "Input Length:" in k:
                input_seq=int(v.split("-")[-1])
            if "Output Length:" in k:
                out_new_seq=int(v.split("-")[-1])
            if "Throughput:" in k:
                query_workload=int(v)
            if "SLO for Latency" in k:
                self.SLO=float(v)
            if "Choose model:" in k:
                if "Top" in v:
                    top_k=int(v.split()[-1])
                else:
                    modellist=[v.lower().replace("llama","model")]

        results = []
        if top_k>=0:
            modellist=modellist[:top_k]
        context_length = input_seq + out_new_seq
        for model_name in modellist:
                print(f"\nChecking model: {model_name} ({model_params[model_name]['actual_name']})")  # Print selected model_name

                for accelerator_name, accelerator_param_set in accelerator_params.items():
                    if model_name in accelerator_param_set:
                        # Fetch accelerator parameters for the current model
                        accelerator_param = accelerator_param_set[model_name]

                        print(f" - Matching accelerator: {accelerator_name} for model {model_name}")  # Checking accelerator mapping

                        # Model and accelerator parameters
                        m_ind = model_params[model_name]['m_ind']
                        dim = model_params[model_name]['dim']
                        dim_hidden = model_params[model_name]['dim_hidden']
                        vocab = model_params[model_name]['vocab']
                        layers = model_params[model_name]['layers']
                        num_head = model_params[model_name]['num_head']
                        num_key_val_heads = model_params[model_name]['num_key_val_heads']
                        head_dim = model_params[model_name]['head_dim']
                        max_context_length = model_params[model_name]['max_context_length']
                        parameters = model_params[model_name]['parameters']
                        actual_name = model_params[model_name]['actual_name']

                        a_ind = accelerator_param['a_ind']
                        dp = accelerator_param['dp']
                        max_storage = accelerator_param['max_storage']
                        compute_bw = accelerator_param['compute_bw']
                        memory_bw = accelerator_param['memory_bw']
                        inter_comm = accelerator_param['Inter_comm']
                        num_chips = accelerator_param['max_chips']
                        max_batch = accelerator_param['max_batch']
                        instance_name = accelerator_param['instance']

                        print(f" -- Accelerator parameters: chips: {num_chips}, compute_bw: {compute_bw}, memory_bw: {memory_bw}")

                        # Compute derived values
                        compute_bw_max = num_chips * compute_bw
                        memory_bw_max = num_chips * memory_bw

                        if context_length > max_context_length:
                            context_length = max_context_length

                        optimal_result = None  # To track the optimal result for this accelerator
                        import math
                        # Loop through powers of 2 batch sizes
                        for batch in [2**i for i in range(int(math.log2(1)), int(math.log2(max_batch)) + 1)]:
                            print(f" -- Testing batch size: {batch}")

                            # Compute F, M, S metrics
                            total_Fall, total_F1, total_F2, total_F3, total_F5 = self.compute_f_metrics(
                                batch, input_seq, dim, dim_hidden, vocab, layers, num_head, num_key_val_heads, head_dim,
                                out_new_seq, max_context_length, parameters
                            )

                            total_Mall, total_M1, total_M2, total_M3, total_M4, total_M5 = self.compute_m_metrics(
                                batch, input_seq, dim, dim_hidden, vocab, layers, num_head, num_key_val_heads, head_dim,
                                out_new_seq, max_context_length, parameters
                            )

                            total_Sall, total_S1, total_S2, total_S3, total_S4, total_KV = self.compute_s_metrics(
                                batch, input_seq, dim, dim_hidden, vocab, layers, num_head, num_key_val_heads, head_dim,
                                out_new_seq, max_context_length, parameters
                            )

                            # Features for the model
                            features = [
                                m_ind,
                                a_ind,
                                parameters,
                                num_chips,
                                compute_bw_max,
                                memory_bw_max,
                                inter_comm,
                                batch,
                                input_seq,
                                out_new_seq,
                                context_length,
                                max_context_length,
                                dim,
                                dim_hidden,
                                vocab,
                                layers,
                                num_head,
                                num_key_val_heads,
                                head_dim,
                                max_storage,
                                total_F1,
                                total_F2,
                                total_F3,
                                total_F5,
                                total_M1,
                                total_M2,
                                total_M3,
                                total_M4,
                                total_M5,
                                total_KV
                            ]

                            print(f" -- Features: {features[:5]}... (truncated)")  # Print part of the features for checking
                            import pickle
                            with open('best_xgb_model_2.pkl', 'rb') as f:
                                loaded_model = pickle.load(f)

                            # Predict latency
                            latency = loaded_model.predict(np.array([features]))
                            qph = (3600 * batch) * dp / latency[0]
                            total_instances = math.ceil(query_workload / qph)
                            total_cost = total_instances * accelerator_param['cost']
                            throughput_per_dollar = qph / total_cost

                            # Create result for this batch
                            result = {
                                "model_name": actual_name,
                                "accelerator_name": accelerator_name,
                                "predicted_latency": latency[0],
                                "total_cost": total_cost,
                                "throughput_per_dollar": throughput_per_dollar,
                                "Optimal Batch": batch,
                                "total_instances": total_instances,
                                "instance_name": instance_name,
                            }

                            # Check if the latency is less than the SLO and update the optimal result
                            latency_diff = self.SLO - latency[0]
                            #if latency[0] < self.SLO and (optimal_result is None or latency_diff < (self.SLO - optimal_result["predicted_latency"])):
                            optimal_result = result

                        # Ensure only the optimal result for the current accelerator is added to the final results
                        if optimal_result:
                            results.append(optimal_result)
                        else:
                            results.append(optimal_result)
                            # If no valid batch size found, append a result with infinite latency
                            '''
                            results.append({
                                "model_name": actual_name,
                                "accelerator_name": accelerator_name,
                                "predicted_latency": float('inf'),  # Infinity to indicate failure to meet SLO
                                "total_cost": float('inf'),
                                "throughput_per_dollar": 0,
                                "Optimal Batch": "N/A",
                                "total_instances": "N/A",
                                "instance_name": instance_name,
                            })'''

        #msgBox = QMessageBox()
        #msgBox.setIcon(QMessageBox.Information)
        print("results/////////////////////////////////")
        print(results)
        self.results=results
        self.reslat=[]
        minlat=999999
        self.modullistname=[]
        for modul in modellist:
            self.modullistname.append(model_params[modul]["actual_name"])
        self.acceleratorname=[]
        for accelerators in list(accelerator_params.keys()):
            self.acceleratorname.append(accelerators)

        self.latencies=[]
        self.costs=[]
        self.throughputs=[]

        for i in self.modullistname:
            latenciesnow=[]
            costsnow=[]
            throughputsnow=[]

            for j in self.acceleratorname:
                flag=False
                for res in results:
                    if res["model_name"]==i and res["accelerator_name"]==j:
                        flag=True
                        if not math.isinf(res["predicted_latency"]):
                            latenciesnow.append(res["predicted_latency"])
                        else:
                            latenciesnow.append(0)
                        costsnow.append(res["total_cost"])
                        throughputsnow.append(res["throughput_per_dollar"])
                if flag==False:
                    latenciesnow.append(0)
                    costsnow.append(0)
                    throughputsnow.append(0)
            self.latencies.append(latenciesnow)
            self.costs.append(costsnow)
            self.throughputs.append(throughputsnow)
        #print(latencies)
        '''
        for modul in modellist:
            print(model_params[modul])
            modulname=model_params[modul]["actual_name"]
            bestlat=None
            minlat=99999999999999999
            for res in results:
                print(res)
                if res["predicted_latency"]<minlat and res["model_name"]==modulname:
                    minlat=res["predicted_latency"]
                    bestlat=res
            self.reslat.append(bestlat)
        self.rescost=[]

        for modul in modellist:
            modulname=model_params[modul]["actual_name"]
            bestcost=None
            mincost=999999.9
            for res in results:

                if float(res["total_cost"])<mincost and res["model_name"]==modulname:
                    mincost=float(res["total_cost"])
                    bestcost=res

            self.rescost.append(bestcost)
            #resmsg+=str(res)+"\n"
        self.resthrough=[]

        for modul in modellist:
            modulname=model_params[modul]["actual_name"]
            bestthrough=None
            maxthrough=-1
            for res in results:
                if res["throughput_per_dollar"]>maxthrough and res["model_name"]==modulname:
                    maxthrough=res["throughput_per_dollar"]
                    bestthrough=res
            self.resthrough.append(bestthrough)

        resmsg="Best Latency:\n\n"

        for a in self.reslat:
            resmsg+=str(a)+"\n"
        resmsg+="Best Cost:\n\n"
        for a in self.rescost:
            resmsg+=str(a)+"\n"
        resmsg+="Best Throughput:\n\n"
        for a in self.resthrough:
            resmsg+=str(a)+"\n"
        #msgBox.setText(resmsg)
        #returnValue = msgBox.exec()
        '''
        self.create_main_window()



    def create_main_window(self):
        # Create the main window
        self.window = QMainWindow()
        self.window.setWindowTitle("Grouped Bar Graph and Data Table in PyQt5")
        self.window.setGeometry(20, 20, 1300, 700)  # Adjusted height for the graph and buttons

        # Create a central widget
        central_widget = QWidget()
        self.window.setCentralWidget(central_widget)

        # Create a layout and set it on the central widget
        layout = QVBoxLayout(central_widget)

        # Create a Matplotlib figure and canvas
        self.window.figure = plt.Figure(figsize=(40, 10), dpi=100)
        self.window.canvas = FigureCanvas(self.window.figure)
        self.window.canvas.setFixedWidth(1300)  # Set canvas width (in pixels)
        self.window.canvas.setFixedHeight(630)  # Set canvas height (in pixels)
        layout.addWidget(self.window.canvas)

        # Add the button
        self.window.next_button = QPushButton("Next: Choose your deployment")
        layout.addWidget(self.window.next_button)
        self.window.next_button.clicked.connect(self.choosedeployment)

        # Create subplots
        self.ax = self.window.figure.add_subplot(311)  # 3-row, 1-column, first plot (top)
        self.ax2 = self.window.figure.add_subplot(312)  # 3-row, 1-column, second plot (middle)
        self.ax3 = self.window.figure.add_subplot(313)  # 3-row, 1-column, third plot (bottom)
        self.window.figure.subplots_adjust(left=0.08, top=0.95, hspace=0.5)

        # Call method to show the graph and table
        self.show_graph_and_table()

        self.window.show()



    def update_combo4(self, selected_category):

        self.combo_box222.clear()
        self.combo_box222.addItems(self.modelmap[selected_category])


    def choosedeployment(self):
        self.modelmap = {}
        for k in self.results:
            if k["model_name"] not in self.modelmap:
                self.modelmap[k["model_name"]] = [k["accelerator_name"] + "; Batch Size: " + str(k["Optimal Batch"])]
            else:
                self.modelmap[k["model_name"]].append(k["accelerator_name"] + "; Batch Size: " + str(k["Optimal Batch"]))
        print(self.modelmap)
        self.formGroupBox1cd = QGroupBox()
        self.formGroupBox1cd.setWindowTitle("GenAI Input Parameters")
        self.formGroupBox1cd.setWindowIcon(QtGui.QIcon('mapleicon.png'))
        self.layout1 = QFormLayout()

        self.c11 = QLabel("Please enter your GenAI Workload Parameters:")
        self.c11.setAlignment(Qt.AlignCenter)
        self.layout1.addRow(self.c11)

        self.label1 = QLabel("Model: ")
        self.combo_box1 = QComboBox()
        self.combo_box1.addItems(list(self.modelmap.keys()))
        self.layout1.addRow(self.label1, self.combo_box1)

        self.combo_box222 = QComboBox()
        self.update_combo4( self.combo_box1.currentText())

        self.label1 = QLabel("Accelerator and Batch Size: ")
        self.layout1.addRow(self.label1, self.combo_box222)

        self.combo_box1.currentTextChanged.connect(self.update_combo4)

        self.ok_button = QPushButton("Submit")
        self.cancel_button = QPushButton("Cancel")
        self.HBox = QHBoxLayout()
        self.HBox.addWidget(self.ok_button)
        self.HBox.addWidget(self.cancel_button)
        self.layout1.addRow(self.HBox)
        self.ok_button.clicked.connect(self.setec2attr)
        self.cancel_button.clicked.connect(self.ec2returner)
        self.formGroupBox1cd.setLayout(self.layout1)
        self.formGroupBox1cd.show()

    def setec2attr(self):
        dicti={}
        for i in range(self.layout1.count()):
          item = self.layout1.itemAt(i)
          if item and isinstance(item.widget(),QLineEdit):
            val = item.widget().text()
            key = self.layout1.labelForField(item.widget()).text()
            dicti[key]=val
          elif item and isinstance(item.widget(),QComboBox):
            key = self.layout1.labelForField(item.widget()).text()
            val = item.widget().currentText()
            dicti[key]=val


        for k,v in dicti.items():
            if "Model:" in k:
                self.attributes["model"]=v
            if "Accelerator and Batch Size:" in k:
                self.attributes["accelerator"]=v.split(";")[0]
                self.attributes["batch_size"]=v.split(":")[-1].strip()
        self.formGroupBox1cd.close()
        self.window.close()


    def ec2returner(self):
        self.formGroupBox1cd.close()
        self.window.close()

    def show_graph_and_table(self):
        import numpy as np
        x = self.modullistname
        z = self.acceleratorname
        y = np.array(self.latencies)
        y[np.isinf(y)] = 0
        width = 0.07  # width of the bars
        x_pos = np.arange(len(x))  # positions for the x-axis labels

        for i, z_value in enumerate(z):
            self.ax.bar(x_pos + i * width, y[:, i], width, label=z_value)

        #self.ax.set_xlabel('Categories')
        self.ax.set_ylabel('Latencies')
        self.ax.set_title('Model-Accelerator Graph')
        #self.ax.set_xticks(x_pos + width / 2)
        #self.ax.set_xticklabels(x)
        max_latency = np.max(y)  # Find the maximum value of throughputs
        #self.ax.set_ylim(0, max_latency * 1.1)
        self.ax.axhline(y=self.SLO, color='red', linestyle='--')
        self.ax.set_xticks([])
        self.ax.set_xticklabels([])
        self.ax.legend(loc='upper right', bbox_to_anchor=(1.12, 1), title="Accelerators")
        ###############################################################

        y = np.array(self.costs)
        y[np.isinf(y)] = 0
        for i, z_value in enumerate(z):
            self.ax2.bar(x_pos + i * width, y[:, i], width, label=z_value)

       # self.ax2.set_xlabel('Categories')
        self.ax2.set_ylabel('Cost(USD)')
        #self.ax2.set_title('Grouped Bar Graph Example')
        #self.ax2.set_xticks(x_pos + width / 2)
        #self.ax2.set_xticklabels(x)
        max_cost = np.max(y)  # Find the maximum value of throughputs
        self.ax2.set_ylim(0, max_cost * 1.11)
        self.ax2.set_xticks([])
        self.ax2.set_xticklabels([])
        #self.ax2.legend(loc='upper right', bbox_to_anchor=(1.12, 1), title="Accelerators")

        ###################################################################

        y = np.array(self.throughputs)
        y[np.isinf(y)] = 0
        for i, z_value in enumerate(z):
            self.ax3.bar(x_pos + i * width, y[:, i], width, label=z_value)

        self.ax3.set_xlabel('Models')
        self.ax3.set_ylabel('Throughput')
        #self.ax3.set_title('Grouped Bar Graph Example')
        max_throughput = np.max(y)  # Find the maximum value of throughputs
        self.ax3.set_ylim(0, max_throughput * 1.12)
        self.ax3.set_xticks(x_pos + width / 2)
        self.ax3.set_xticklabels(x)
        #self.ax3.legend(loc='upper right', bbox_to_anchor=(1.12, 1), title="Accelerators")

        self.window.canvas.draw()


    def returner(self):
        self.formGroupBox1ash.close()

    def update_combo2(self, selected_category):
        # Clear the current items in combo2
        self.text_box222.clear()

        # Populate combo2 based on the selected category
        inptoklist=[]
        if selected_category=="Text Generation":
            inptoklist=["Short-50","Medium-100","Long-150"]
        elif selected_category=="Summarization":
            inptoklist=["Short-250","Medium-500","Long-750"]
        elif selected_category=="Q&A":
            inptoklist=["Short-50","Medium-100","Long-150"]
        elif selected_category=="Bi-lingual Translation":
            inptoklist=["Short-250","Medium-500","Long-750"]
        self.text_box222.addItems(inptoklist)

    def update_combo3(self, selected_category):
        # Clear the current items in combo2
        self.text_box333.clear()

        # Populate combo2 based on the selected category
        inptoklist=[]
        if selected_category=="Text Generation":
            inptoklist=["Short-150","Medium-250","Long-500"]
        elif selected_category=="Summarization":
            inptoklist=["Short-100","Medium-200","Long-300"]
        elif selected_category=="Q&A":
            inptoklist=["Short-50","Medium-150","Long-300"]
        elif selected_category=="Bi-lingual Translation":
            inptoklist=["Short-250","Medium-500","Long-750"]
        self.text_box333.addItems(inptoklist)




    def llmform(self):
        self.formGroupBox2tu.close()
        self.formGroupBox1ash = QGroupBox()
        self.formGroupBox1ash.setWindowTitle("GenAI Input Parameters")
        self.formGroupBox1ash.setWindowIcon(QtGui.QIcon('mapleicon.png'))
        self.layout1 = QFormLayout()

        self.c11 = QLabel("Please enter your GenAI Workload Parameters:")
        self.c11.setAlignment(Qt.AlignCenter)
        self.layout1.addRow(self.c11)
        self.label1 = QLabel("Throughput: ")
        self.text_box1 = QLineEdit("1000")
        self.layout1.addRow(self.label1,self.text_box1)
        self.label1 = QLabel("Application type: ")
        self.text_box1 = QComboBox()
        self.text_box1.addItems(["Text Generation","Summarization","Q&A","Bi-lingual Translation"])
        self.layout1.addRow(self.label1,self.text_box1)
        self.text_box222 = QComboBox()
        self.update_combo2(self.text_box1.currentText())

        self.label1 = QLabel("Input Length: ")


        self.layout1.addRow(self.label1,self.text_box222)
        self.text_box1.currentTextChanged.connect(self.update_combo2)

        self.label1 = QLabel("Output Length: ")
        self.text_box333 = QComboBox()
        self.update_combo3(self.text_box1.currentText())
        self.layout1.addRow(self.label1,self.text_box333)
        self.text_box1.currentTextChanged.connect(self.update_combo3)
        self.label1 = QLabel("Use Batching? ")
        self.text_box1 = QComboBox()
        self.text_box1.addItems(["Yes","No"])
        self.layout1.addRow(self.label1,self.text_box1)
        self.label1 = QLabel("SLO for Latency (seconds): ")
        self.text_box1 = QLineEdit("10")
        self.layout1.addRow(self.label1,self.text_box1)
        self.label1 = QLabel("Choose model: ")
        self.text_box1 = QComboBox()
        self.text_box1.addItems(["Top 1", "Top 2", "Top 3","Top 4","Top 5","Top 6","Llama_7b","Llama_13b","Llama_70b","Mistral_7b"])
        self.layout1.addRow(self.label1,self.text_box1)
        ok_button = QPushButton("Okay")
        cancel_button = QPushButton("Cancel")
        ok_button.clicked.connect(self.carllm)
        cancel_button.clicked.connect(self.returner)
        HBox = QHBoxLayout()
        HBox.addWidget(ok_button)
        HBox.addWidget(cancel_button)
        self.layout1.addRow(HBox)
        self.formGroupBox1ash.setLayout(self.layout1)
        self.formGroupBox1ash.show()

    def drawraguru(self):
        self.formGroupBox1raguru.close()
        dicti={}
        for i in range(self.layout2222.count()):
          item = self.layout2222.itemAt(i)
          if item and isinstance(item.widget(),QLineEdit):
            val = item.widget().text()
            key = self.layout2222.labelForField(item.widget()).text()
            dicti[key]=val
          elif item and isinstance(item.widget(),QComboBox):
            key = self.layout2222.labelForField(item.widget()).text()
            val = item.widget().currentText()
            dicti[key]=val
        from Diagram import GraphicView
        #GraphicView.
        self.addOptrag(self.ragllm,"CSP",0,0,"both",-95)
        self.addOptrag(self.ragllm,"CSP",0,0,"both",0)
        if dicti["Where do you want to deploy the VectorDB: "]=="Different EC2":
            self.addOptrag("VectorDB","CSP",0,0,"both",95)
        from Vpcsquare import CustomWidget
        border_color = QColor(255, 0, 0)  # Red color
        border_thickness = 3
        square_sizey = 75+105+50
        square_sizex= 300
        top_left_x = self.x()-105
        top_left_y = self.y()-15
        cswid = importlib.import_module("CSWidgets")
        cw=CustomWidget(square_sizex,square_sizey,top_left_x,top_left_y,border_color,border_thickness)
        cw.setZValue(0)
        cswid.diagram.scene.addItem(cw)
        text_item=QGraphicsTextItem("RAG")
        text_item.setDefaultTextColor(QColor(Qt.black))
        text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
        text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
        text_item.setPos(QPointF(self.x()-95,self.y()+5))
        font = QFont()
        font.setPointSize(15)  # Increase the font size to 16

                    # Apply the font to the QGraphicsTextItem
        text_item.setFont(font)
        cswid.diagram.scene.addItem(text_item)




    def addOptrag(self,newServiceName,CSPname,newlatency,newcost,dir,x):
        print("Insid replace "+newServiceName)
        '''
        if radiobgcp.isChecked():
            print(replaceService[0])
            ServiceName = replaceService[0]
            CSPname = "GCP"
        elif radiobazure.isChecked():
            print(replaceService[1])
            ServiceName = replaceService[1]
            CSPname = "AZR"
            '''
        #print("Insid replace ----- "+replaceServicemain)
        ptX = self.x()
        ptY = self.y()
        print(str(self.x()))
        print(str(self.y()))
        #self.scene.addItem(self.shape)
        #self.shape.setPos(QPointF(position.x(), position.y()))
        #cswid = importlib.import_module("CSWidgets")
        #cswid.widget5.clear()
        #cswid.widget5.addItem(serviceName)
        rownum = 0
        cswid = importlib.import_module("CSWidgets")
        #items = cswid.diagram.scene.selectedItems()
        #for item in items:
        print("-----------------------------")
        print(self)
        print(newServiceName)
        print("-----------------------------")
        #awslist.append("Amazon DynamoDB Accelerator (DAX)")
        #cswid.diagram.scene.removeItem(self)
        #for item in cswid.diagram.scene.items():
        #    if isinstance(item,QGraphicsTextItem) and item.toPlainText()==self.name:
        #        print(item)
        #        cswid.diagram.scene.removeItem(item)
        shape=0
        if CSPname == "CSP":
            for iterator in awslist:
                rownum += 1
                if newServiceName == iterator:#.split('^')[0]:
                    #shape = Shape("CSP",100,50,0,0,rownum)
                    shape = Shape(CSPname,100,50,newlatency,newcost,rownum)
                    shape.Latency=newlatency
                    shape.Cost=newcost
                    shape.setPos(QPointF(ptX+x, ptY+50+75))
                    cswid.diagram.addObject(shape)
                    cswid.diagram.scene.addItem(shape)
                    text_item=QGraphicsTextItem(newServiceName)
                    text_item.setDefaultTextColor(QColor(Qt.black))
                    text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
                    text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
                    text_item.setPos(QPointF(ptX+x,ptY+75+105))
                    shape.textItem = text_item
                    font = QFont()
                    font.setPointSize(15)  # Increase the font size to 16

                    # Apply the font to the QGraphicsTextItem
                    text_item.setFont(font)
                    cswid.diagram.scene.addItem(text_item)

                    if (dir=="from" or dir=="both"):
                        for ii in self.out_arrows:
                            #cswid.diagram.scene.removeItem(ii)
                            cswid.diagram.addArrowopt(shape,self,False)
                        #cswid.diagram.addArrowopt(self,shape,False)
                    if (dir=="to" or dir=="both"):
                        for ii in self.in_arrows:
                            #cswid.diagram.scene.removeItem(ii)
                            cswid.diagram.addArrowopt(self,shape,False)
                        #cswid.diagram.addArrowopt(shape,self,False)
                    #cswid.diagram.addArrowopt(shape,self)
        elif CSPname == "GCP":
            for iterator in gcplist:
                rownum += 1
                if newServiceName == iterator:#.split('^')[0]:
                    #shape = Shape("CSP",100,50,0,0,rownum)
                    shape = Shape(CSPname,100,50,0,0,rownum)
                    shape.setPos(QPointF(ptX, ptY))
                    cswid.diagram.addObject(shape)
                    cswid.diagram.scene.addItem(shape)
        elif CSPname == "AZR":
            for iterator in azurelist:
                rownum += 1
                if newServiceName == iterator:#.split('^')[0]:
                    #shape = Shape("CSP",100,50,0,0,rownum)
                    shape = Shape(CSPname,100,50,0,0,rownum)
                    shape.setPos(QPointF(ptX, ptY))
                    cswid.diagram.addObject(shape)
                    cswid.diagram.scene.addItem(shape)

        for iterator in azurelist:
            rownum += 1
            if newServiceName == iterator:#.split('^')[0]:
                #shape = Shape("CSP",100,50,0,0,rownum)
                shape = Shape(CSPname,100,50,0,0,rownum)
                shape.setPos(QPointF(ptX, ptY))
                cswid.diagram.addObject(shape)
                cswid.diagram.scene.addItem(shape)

        #cswid.diagram.calcCostLatency()
        #self.formGroupBox.close()
        return shape





    def raguru(self):
        import CSWidgets
        CSWidgets.totLateDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(6.968))+" sec")
        CSWidgets.totCostDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(1776.024))+" $/month")

        self.formGroupBox1rag.close()
        dicti={}
        for i in range(self.layout1.count()):
          item = self.layout1.itemAt(i)
          if item and isinstance(item.widget(),QLineEdit):
            val = item.widget().text()
            key = self.layout1.labelForField(item.widget()).text()
            dicti[key]=val
          elif item and isinstance(item.widget(),QComboBox):
            key = self.layout1.labelForField(item.widget()).text()
            val = item.widget().currentText()
            dicti[key]=val
        reply=suggest_rag_architecture({"num_users":dicti["No. of users: "],"num_documents":dicti["No. of documents: "],"doc_type":dicti["Document Type: "],"query_type":dicti["Query Complexity: "]})
        print(reply)
        self.formGroupBox1raguru = QGroupBox()
        self.formGroupBox1raguru.setWindowTitle("RAG Input Parameters")
        self.formGroupBox1raguru.setWindowIcon(QtGui.QIcon('mapleicon.png'))
        self.layout2222 = QFormLayout()
        self.label1 = QLabel("Embedding Model: "+reply['Embedding Model'])
        self.layout2222.addRow(self.label1)
        self.label1 = QLabel("LLM: "+reply['LLM'])
        self.layout2222.addRow(self.label1)
        self.label1 = QLabel("'Vector DB': "+reply['Vector DB'])
        self.layout2222.addRow(self.label1)
        self.label1 = QLabel("Latency: "+str(reply['Latency']))
        self.layout2222.addRow(self.label1)
        self.label1 = QLabel("Cost: "+str(reply['Cost']))
        self.layout2222.addRow(self.label1)
        self.label1 = QLabel("Where do you want to deploy the VectorDB: ")
        self.text_box1 = QComboBox()
        self.text_box1.addItems(["Same EC2","Different EC2"])
        self.layout2222.addRow(self.label1,self.text_box1)
        ok_button = QPushButton("Back")
        cancel_button = QPushButton("Ok")
        self.ragllm=reply['LLM']
        self.ragembed=reply['Embedding Model']
        self.Cost=reply['Cost']
        self.Latency=reply['Latency']
        self.attributes["Cost"]=reply['Cost']
        self.attributes["Latency"]=reply['Latency']
        ok_button.clicked.connect(self.rag)
        cancel_button.clicked.connect(self.drawraguru)
        HBox = QHBoxLayout()
        HBox.addWidget(ok_button)
        HBox.addWidget(cancel_button)
        self.layout2222.addRow(HBox)
        self.formGroupBox1raguru.setLayout(self.layout2222)
        self.formGroupBox1raguru.show()

    def rag(self):
        self.formGroupBox2tu.close()
        self.formGroupBox1rag = QGroupBox()
        self.formGroupBox1rag.setWindowTitle("RAG Input Parameters")
        self.formGroupBox1rag.setWindowIcon(QtGui.QIcon('mapleicon.png'))
        self.layout1 = QFormLayout()

        self.c11 = QLabel("Please enter your GenAI Workload Parameters:")
        self.c11.setAlignment(Qt.AlignCenter)
        self.layout1.addRow(self.c11)
        self.label1 = QLabel("No. of users: ")
        self.text_box1 = QComboBox()
        self.text_box1.addItems(["<10","10 to 50"])
        self.layout1.addRow(self.label1,self.text_box1)
        self.label1 = QLabel("No. of documents: ")
        self.text_box1 = QComboBox()
        self.text_box1.addItems(["Upto 50", "50 to 100", "100 to 500"])
        self.layout1.addRow(self.label1,self.text_box1)
        self.label1 = QLabel("Document Type: ")
        self.text_box1 = QComboBox()
        self.text_box1.addItems(["Large documents", "Invoices", "Small documents"])
        self.layout1.addRow(self.label1,self.text_box1)
        self.label1 = QLabel("Query Complexity: ")
        self.text_box1 = QComboBox()
        self.text_box1.addItems(["Simple","Complex"])
        self.layout1.addRow(self.label1,self.text_box1)
        ok_button = QPushButton("Submit")
        cancel_button = QPushButton("Cancel")
        ok_button.clicked.connect(self.raguru)
        cancel_button.clicked.connect(self.returner)
        HBox = QHBoxLayout()
        HBox.addWidget(ok_button)
        HBox.addWidget(cancel_button)
        self.layout1.addRow(HBox)
        self.formGroupBox1rag.setLayout(self.layout1)
        self.formGroupBox1rag.show()

    def normalec2(self):
        self.formGroupBox2tu.close()
        self.layout = QFormLayout()
        self.formGroupBox = QGroupBox()
        for k,v in serviceCodeToDropdownMapping[serviceNameToCodeMapping[self.name]].items():
            label = QLabel(k)
            text_box = QComboBox()
            attrs = v
            print("While creating "+self.name)
            print(type(text_box))
            text_box.addItems(v)
            text_box.setCurrentIndex(0)
            text_box.setFixedWidth(300)
            self.layout.addRow(label,text_box)
            #self.layout.addWidget(self.label)
            #self.layout.addWidget(self.text_box)

        #self.submit_button = QPushButton("Submit")
        # self.layout.addRow(self.submit_button)

        self.update_button = QPushButton("Update")
        self.apply_button = QPushButton("Apply")
        self.HBOX  = QHBoxLayout()
        # sku = self.update_button.clicked.connect(self.runCostModel)
        self.update_button.clicked.connect(self.runCostModel)
        # self.apply_button.clicked.connect(self.applyAttributes)
        self.apply_button.clicked.connect(self.applyAttributes)
        self.HBOX.addWidget(self.update_button)
        self.HBOX.addWidget(self.apply_button)
        self.layout.addRow(self.HBOX)
        # self.layout.addRow(QLabel("Cost :: 123456 $     Latency :: 123 secs"))
        self.formGroupBox.setLayout(self.layout)
        #self.submit_button.clicked.connect(self.displayCustCostSubmit)
        self.formGroupBox.show()

    def CustCostPerf(self):


        if(self.text=="CSP"):
            if(self.name=="Amazon Simple Storage System (S3)"):
                self.formGroupBox = QGroupBox()
                self.formGroupBox.setWindowTitle("Custom Cost && Performance")
                self.formGroupBox.setWindowIcon(QtGui.QIcon('mapleicon.png'))
                self.layout = QFormLayout()
                self.tabWidget = QTabWidget()
                tab1 = QWidget()
                tab2 = QWidget()
                tab3 = QWidget()
                self.tabWidget.addTab(tab1,"Storage")
                self.tabWidget.addTab(tab2,"Outbound")
                self.tabWidget.addTab(tab3,"Inbound")
                self.c1 = QLabel("Performance Parameters")
                self.c1.setAlignment(Qt.AlignCenter)
                self.layout.addRow(self.c1)

                for param in self.paramList.split("^"):
                    self.label = QLabel(param+" ")
                    self.text_box = QLineEdit()
                    self.layout.addRow(self.label,self.text_box)

                self.update_button = QPushButton("Update")
                self.apply_button = QPushButton("Apply")
                self.HBOX  = QHBoxLayout()
                # sku = self.update_button.clicked.connect(self.runCostModel)
                self.update_button.clicked.connect(self.runCostModel)
                # self.apply_button.clicked.connect(self.applyAttributes)
                self.apply_button.clicked.connect(self.applyAttributes)
                self.HBOX.addWidget(self.update_button)
                self.HBOX.addWidget(self.apply_button)
                self.layout.addRow(self.HBOX)
                # self.layout.addRow(QLabel("Cost :: 123456 $     Latency :: 123 secs"))
                self.formGroupBox.setLayout(self.layout)
                #self.submit_button.clicked.connect(self.displayCustCostSubmit)
                self.formGroupBox.show()
                    #self.layout.addWidget(self.label)
                    #self.layout.addWidget(self.text_box)

                #self.submit_button = QPushButton("Submit")
                #self.layout.addRow(self.submit_button)
                #self.formGroupBox.setLayout(self.layout)
                #self.formGroupBox.show()

                #self.submit_button.clicked.connect(self.calcCustom)

                #self.formGroupBox = QGroupBox()
                #self.formGroupBox.setWindowTitle("Custom Cost")
                #self.formGroupBox.setWindowIcon(QtGui.QIcon('mapleicon.png'))
                # #self.layout = QFormLayout()
                # self.c2 = QLabel("Cost Parameters")
                # self.c2.setAlignment(Qt.AlignCenter)
                # self.layout.addRow(self.c2)
                # self.layout.addRow(self.tabWidget)

                # tab1_layout = QFormLayout()
                # # tab1_layout.addRow(QLabel("Storage Label t11"), QLineEdit())
                # # tab1_layout.addRow(QLabel("Storage Label t12"), QLineEdit())
                # for k,v in serviceCodeToDropdownMapping[serviceNameToCodeMapping[self.name]+'Tab1'].items():
                #     label = QLabel(k)
                #     text_box = QComboBox()
                #     attrs = v
                #     # print(v)
                #     text_box.addItems(v)
                #     text_box.setCurrentIndex(0)
                #     text_box.setFixedWidth(300)
                #     tab1_layout.addRow(label,text_box)
                #     #self.layout.addWidget(self.label)
                #     #self.layout.addWidget(self.text_box)
                # update_button = QPushButton("Update")
                # apply_button = QPushButton("Apply")
                # HBOX  = QHBoxLayout()
                # update_button.clicked.connect(self.runCostModel)
                # # apply_button.clicked.connect(self.applyAttributes)
                # apply_button.clicked.connect(self.applyAttributes)
                # HBOX.addWidget(update_button)
                # HBOX.addWidget(apply_button)
                # tab1_layout.addRow(HBOX)
                # tab1.setLayout(tab1_layout)

                # tab2_layout = QFormLayout()
                # # tab2_layout.addRow(QLabel("Storage Label t21"), QLineEdit())
                # # tab2_layout.addRow(QLabel("Storage Label t22"), QLineEdit())
                # for k,v in serviceCodeToDropdownMapping[serviceNameToCodeMapping[self.name]+'Tab2'].items():
                #     label = QLabel(k)
                #     text_box = QComboBox()
                #     attrs = v
                #     # print(v)
                #     text_box.addItems(v)
                #     text_box.setCurrentIndex(0)
                #     text_box.setFixedWidth(300)
                #     tab2_layout.addRow(label,text_box)
                #     #self.layout.addWidget(self.label)
                #     #self.layout.addWidget(self.text_box)
                # update_button = QPushButton("Update")
                # apply_button = QPushButton("Apply")
                # HBOX  = QHBoxLayout()
                # update_button.clicked.connect(self.runCostModel)
                # # apply_button.clicked.connect(self.applyAttributes)
                # apply_button.clicked.connect(self.applyAttributes)
                # HBOX.addWidget(update_button)
                # HBOX.addWidget(apply_button)
                # tab2_layout.addRow(HBOX)
                # tab2.setLayout(tab2_layout)

                # tab3_layout = QFormLayout()
                # # tab3_layout.addRow(QLabel("Storage Label t31"), QLineEdit())
                # # tab3_layout.addRow(QLabel("Storage Label t32"), QLineEdit())
                # for k,v in serviceCodeToDropdownMapping[serviceNameToCodeMapping[self.name]+'Tab3'].items():
                #     label = QLabel(k)
                #     text_box = QComboBox()
                #     attrs = v
                #     # print(v)
                #     text_box.addItems(v)
                #     text_box.setCurrentIndex(0)
                #     text_box.setFixedWidth(300)
                #     tab3_layout.addRow(label,text_box)
                #     #self.layout.addWidget(self.label)
                #     #self.layout.addWidget(self.text_box)

                # update_button = QPushButton("Update")
                # apply_button = QPushButton("Apply")
                # HBOX  = QHBoxLayout()
                # update_button.clicked.connect(self.runCostModel)
                # # apply_button.clicked.connect(self.applyAttributes)
                # apply_button.clicked.connect(self.applyAttributes)
                # HBOX.addWidget(update_button)
                # HBOX.addWidget(apply_button)
                # tab3_layout.addRow(HBOX)
                # tab3.setLayout(tab3_layout)

                # self.formGroupBox.setLayout(self.layout)
                # self.formGroupBox.show()

            elif self.name=="Amazon EC2":
                self.formGroupBox2tu = QGroupBox()
                self.formGroupBox2tu.setWindowTitle("Architechture Type")
                self.formGroupBox2tu.setWindowIcon(QtGui.QIcon('mapleicon.png'))
                self.layout2 = QFormLayout()

                self.c22 = QLabel("What do you want tp deploy on this EC2?")
                self.c22.setAlignment(Qt.AlignCenter)
                self.layout2.addRow(self.c22)
                ok_button = QPushButton("LLM")
                cancel_button = QPushButton("RAG")
                ok_button.clicked.connect(self.llmform)
                cancel_button.clicked.connect(self.rag)
                other_button = QPushButton("Others")

                other_button.clicked.connect(self.normalec2)
                HBox = QHBoxLayout()
                HBox.addWidget(ok_button)
                HBox.addWidget(cancel_button)
                HBox.addWidget(other_button)
                self.layout2.addRow(HBox)
                self.formGroupBox2tu.setLayout(self.layout2)
                self.formGroupBox2tu.show()

            elif self.name=="AWS Lambda":
                self.formGroupBox = QGroupBox()
                self.formGroupBox.setWindowTitle("Custom Cost && Performance")
                self.formGroupBox.setWindowIcon(QtGui.QIcon('mapleicon.png'))
                self.layout = QFormLayout()

                self.c1 = QLabel("Performance Parameters")
                self.c1.setAlignment(Qt.AlignCenter)
                self.layout.addRow(self.c1)

                for param in self.paramList.split("^"):
                    self.label = QLabel(param+" ")
                    if "Function" in param:
                        self.text_box = QComboBox()
                        self.text_box.addItems(["Preprocessing of Diagrams","Text extraction from Diagrams","Lines extraction from Diagrams","Circles extraction from Diagrams","Graph creation of Diagram entities","Equipment extraction from Diagrams","Aggregation of Diagram Entities","OCR - Text detection and extraction from documents","Table extraction from documents","Spatial relationship extraction from documents","Data type extraction from documents"])
                    else:
                        self.text_box = QLineEdit()
                    self.layout.addRow(self.label,self.text_box)
                    #self.layout.addWidget(self.label)
                    #self.layout.addWidget(self.text_box)

                #self.submit_button = QPushButton("Submit")
                #self.layout.addRow(self.submit_button)
                #self.formGroupBox.setLayout(self.layout)
                #self.formGroupBox.show()

                #self.submit_button.clicked.connect(self.calcCustom)

                #self.formGroupBox = QGroupBox()
                #self.formGroupBox.setWindowTitle("Custom Cost")
                #self.formGroupBox.setWindowIcon(QtGui.QIcon('mapleicon.png'))
                #self.layout = QFormLayout()
                self.c2 = QLabel("Cost Parameters")
                self.c2.setAlignment(Qt.AlignCenter)
                self.layout.addRow(self.c2)

                # for k,v in serviceCodeToDropdownMapping[serviceNameToCodeMapping[self.name]].items():
                #     label = QLabel(k)
                #     text_box = QComboBox()
                #     attrs = v
                #     text_box.addItems(v)
                #     text_box.setCurrentIndex(0)
                #     text_box.setFixedWidth(300)
                #     self.layout.addRow(label,text_box)
                #     #self.layout.addWidget(self.label)
                #     #self.layout.addWidget(self.text_box)

                # #self.submit_button = QPushButton("Submit")
                # # self.layout.addRow(self.submit_button)

                self.update_button = QPushButton("Update")
                self.apply_button = QPushButton("Apply")
                self.HBOX  = QHBoxLayout()
                # sku = self.update_button.clicked.connect(self.runCostModel)
                self.update_button.clicked.connect(self.runCostModel)
                # self.apply_button.clicked.connect(self.applyAttributes)
                self.apply_button.clicked.connect(self.applyAttributes)
                self.HBOX.addWidget(self.update_button)
                self.HBOX.addWidget(self.apply_button)
                self.layout.addRow(self.HBOX)
                # self.layout.addRow(QLabel("Cost :: 123456 $     Latency :: 123 secs"))
                self.formGroupBox.setLayout(self.layout)
                #self.submit_button.clicked.connect(self.displayCustCostSubmit)
                self.formGroupBox.show()

                # '''
                # msg = QMessageBox()
                # msg.setWindowTitle("Default Performance")
                # #lat = diagram.getNetLatency()
                # lat = 10
                # msg.setText("Message: " )
                # x = msg.exec_()
                # '''

            else:
                self.formGroupBox = QGroupBox()
                self.formGroupBox.setWindowTitle("Custom Cost && Performance")
                self.formGroupBox.setWindowIcon(QtGui.QIcon('mapleicon.png'))
                self.layout = QFormLayout()

                self.c1 = QLabel("Performance Parameters")
                self.c1.setAlignment(Qt.AlignCenter)
                self.layout.addRow(self.c1)

                for param in self.paramList.split("^"):
                    self.label = QLabel(param+" ")
                    self.text_box = QLineEdit()
                    self.layout.addRow(self.label,self.text_box)
                    #self.layout.addWidget(self.label)
                    #self.layout.addWidget(self.text_box)

                #self.submit_button = QPushButton("Submit")
                #self.layout.addRow(self.submit_button)
                #self.formGroupBox.setLayout(self.layout)
                #self.formGroupBox.show()

                #self.submit_button.clicked.connect(self.calcCustom)

                #self.formGroupBox = QGroupBox()
                #self.formGroupBox.setWindowTitle("Custom Cost")
                #self.formGroupBox.setWindowIcon(QtGui.QIcon('mapleicon.png'))
                #self.layout = QFormLayout()
                self.c2 = QLabel("Cost Parameters")
                self.c2.setAlignment(Qt.AlignCenter)
                self.layout.addRow(self.c2)

                # for k,v in serviceCodeToDropdownMapping[serviceNameToCodeMapping[self.name]].items():
                #     label = QLabel(k)
                #     text_box = QComboBox()
                #     attrs = v
                #     text_box.addItems(v)
                #     text_box.setCurrentIndex(0)
                #     text_box.setFixedWidth(300)
                #     self.layout.addRow(label,text_box)
                #     #self.layout.addWidget(self.label)
                #     #self.layout.addWidget(self.text_box)

                # #self.submit_button = QPushButton("Submit")
                # # self.layout.addRow(self.submit_button)

                self.update_button = QPushButton("Update")
                self.apply_button = QPushButton("Apply")
                self.HBOX  = QHBoxLayout()
                # sku = self.update_button.clicked.connect(self.runCostModel)
                self.update_button.clicked.connect(self.runCostModel)
                # self.apply_button.clicked.connect(self.applyAttributes)
                self.apply_button.clicked.connect(self.applyAttributes)
                self.HBOX.addWidget(self.update_button)
                self.HBOX.addWidget(self.apply_button)
                self.layout.addRow(self.HBOX)
                # self.layout.addRow(QLabel("Cost :: 123456 $     Latency :: 123 secs"))
                self.formGroupBox.setLayout(self.layout)
                #self.submit_button.clicked.connect(self.displayCustCostSubmit)
                self.formGroupBox.show()

                # '''
                # msg = QMessageBox()
                # msg.setWindowTitle("Default Performance")
                # #lat = diagram.getNetLatency()
                # lat = 10
                # msg.setText("Message: " )
                # x = msg.exec_()
                # '''
        if(self.text == "AZR"):
                print('from AZR')
                self.formGroupBox = QGroupBox()
                self.formGroupBox.setWindowTitle("Custom Cost && Performance")
                self.formGroupBox.setWindowIcon(QtGui.QIcon('mapleicon.png'))
                self.layout = QFormLayout()

                self.c1 = QLabel("Performance Parameters")
                self.c1.setAlignment(Qt.AlignCenter)
                self.layout.addRow(self.c1)

                #for param in self.paramList.split("^"):
                #    self.label = QLabel(param+" ")
                #    self.text_box = QLineEdit()
                #    self.layout.addRow(self.label,self.text_box)
                    #self.layout.addWidget(self.label)
                    #self.layout.addWidget(self.text_box)

                #self.submit_button = QPushButton("Submit")
                #self.layout.addRow(self.submit_button)
                #self.formGroupBox.setLayout(self.layout)
                #self.formGroupBox.show()

                #self.submit_button.clicked.connect(self.calcCustom)

                #self.formGroupBox = QGroupBox()
                #self.formGroupBox.setWindowTitle("Custom Cost")
                #self.formGroupBox.setWindowIcon(QtGui.QIcon('mapleicon.png'))
                #self.layout = QFormLayout()
                self.c2 = QLabel("Cost Parameters")
                self.c2.setAlignment(Qt.AlignCenter)
                self.layout.addRow(self.c2)

                print("IN qzure block Looking for "+self.name)
                for k,v in serviceCodeToDropdownMapping[serviceNameToCodeMapping[self.name]].items():
                    label = QLabel(k)
                    text_box = QComboBox()
                    attrs = v
                    print("While creating "+self.name)
                    print(type(text_box))
                    text_box.addItems(v)
                    text_box.setCurrentIndex(0)
                    text_box.setFixedWidth(300)
                    self.layout.addRow(label,text_box)
                    #self.layout.addWidget(self.label)
                    #self.layout.addWidget(self.text_box)

                #self.submit_button = QPushButton("Submit")
                # self.layout.addRow(self.submit_button)

                self.update_button = QPushButton("Update")
                self.apply_button = QPushButton("Apply")
                self.HBOX  = QHBoxLayout()
                # sku = self.update_button.clicked.connect(self.runCostModel)
                self.update_button.clicked.connect(self.runCostModel)
                self.apply_button.clicked.connect(self.applyAttributes)
                self.HBOX.addWidget(self.update_button)
                self.HBOX.addWidget(self.apply_button)
                self.layout.addRow(self.HBOX)
                # self.layout.addRow(QLabel("Cost :: 123456 $     Latency :: 123 secs"))
                self.formGroupBox.setLayout(self.layout)
                #self.submit_button.clicked.connect(self.displayCustCostSubmit)
                self.formGroupBox.show()


    def applyAttributes(self):
        for i in range(self.layout.count()):
            item = self.layout.itemAt(i)
            key=""
            value=""
            if item and isinstance(item.widget(), QComboBox):
                key = self.layout.labelForField(item.widget()).text()
                value = item.widget().currentText()
            if item and isinstance(item.widget(),QLineEdit):
                key = self.layout.labelForField(item.widget()).text().strip()
                val = item.widget().text()
            self.attributes[key] = value
        msg = QMessageBox()
        msg.setWindowTitle("Saved Message")
        msg.setText("Configuration Saved Successfully")
        x = msg.exec_()
        self.formGroupBox.close()
        diagram = importlib.import_module("Diagram")
        diag = diagram.GraphicView()
        #diag.calcCostLatency()


    def runCostModel(self):
        for i in range(self.layout.count()):
            item = self.layout.itemAt(i)
            if isinstance(item.widget(),QLineEdit):
                key = self.layout.labelForField(item.widget()).text().strip()
                val = item.widget().text()
                if val=="": val = 0
                elif key=="workload": val=int(val)
                else:
                    try:
                        val = float(val)
                    except:
                        pass
                # custPerfCost += val
                self.perfAttributes[key] = val
            elif isinstance(item.widget(),QComboBox):
                key = self.layout.labelForField(item.widget()).text().strip()
                val = item.widget().currentText()
                if val=="": val = 0
                elif key=="workload": val=int(val)
                else:
                    try:
                        val = int(val)
                    except:
                        pass
                # custPerfCost += val
                self.perfAttributes[key] = val
        # print("------------------------------\n",self.perfAttributes,"\n---------------------------------")
        # c,l = computeCostAndPerf(self.perfAttributes)
        print(self.perfAttributes)
        if(self.name=='AWS Lambda'):
            op = compute_lambda_latency_and_cost(self.perfAttributes)#['workload'])
        elif(self.name=='Amazon Simple Storage System (S3)'):
            op = compute_s3_latency_and_cost(self.perfAttributes)
        elif(self.name=="Amazon EC2"):
            accel = instanceTypeToAcceleratorMap[self.perfAttributes['instanceType']]
            model = self.perfAttributes['LLMModel'][6:]
            print(model)
            accel_config = accelerator_params[accel][model][1]
            lat = compute_latency(accel_config,self.perfAttributes['batchSize'],self.perfAttributes['inputTokens'],self.perfAttributes['outputTokens'])
            op = {"cost":accelerator_params[accel]['cost'],"latency":lat}
        elif self.name=="Amazon DynamoDB":
            op = compute_dynamodb_latency_and_cost(self.perfAttributes)#['workload'])
        #self.layout.addRow(QLabel("Cost ::"+ str(c)+" $     Latency ::"+str(l)+" secs"))
        print(self.layout.count())
        self.layout.removeRow(self.layout.count() - 2)
        self.layout.addRow(QLabel("Cost ::"+ str(round(op['cost'],3))+" $     Latency ::"+str(round(op['latency'],3))+" secs"))
        self.Cost = op['cost']
        self.Latency = op['latency']
        # print(computeCostAndPerf(self.perfAttributes))
        # print(self.perfAttributes)




    def displayDefaultPerf(self):
        msg = QMessageBox()
        msg.setWindowTitle("Default Performance")
        #lat = diagram.getNetLatency()
        lat = 10
        msg.setText("Message: " + self.Data.split('^')[3])
        x = msg.exec_()

    # def serviceOutputForm(self):
    #     self.formGroupBox = QGroupBox()
    #     self.formGroupBox.setWindowTitle("Service Output")
    #     self.formGroupBox.setWindowIcon(QtGui.QIcon('mapleicon.png'))
    #     self.layout = QFormLayout()

    #     data_types = [
    #     "JSON",
    #     "XML",
    #     "Text/String",
    #     "Numeric Types (Integer, Float/Double)",
    #     "Boolean",
    #     "Binary",
    #     "Date/Time",
    #     "ARN (Amazon Resource Name)",
    #     "URL/URI (Uniform Resource Locator/Identifier)",
    #     "Lists/Arrays",
    #     "Maps/Objects/Dictionaries",
    #     "Error Responses",
    #     "HTML",
    #     "CSV (Comma-Separated Values)",
    #     "Logs/Events",
    #     "Lambda Function Outputs",
    #     ]
    #     combo_box = QComboBox()
    #     for dt in data_types:
    #         combo_box.addItem(dt)
    #     combo_box.setCurrentText("Text/String")
    #     self.layout.addRow("Select an option:", combo_box)

    #     output_submit_button = QPushButton("Submit")
    #     output_submit_button.clicked.connect(self.onOutputSubmitButtonClicked)
    #     self.layout.addRow(output_submit_button)
    #     self.formGroupBox.setLayout(self.layout)
    #     # self.setCentralWidget(self.formGroupBox)
    #     self.formGroupBox.show()

    def onOutputSubmitButtonClicked(self):
        print(self.name)
        service_index = awslist.index(self.name)
        service_values_list = nextServiceMap[service_index]
        top3Services = sorted(range(len(service_values_list)), key=lambda i: service_values_list[i], reverse=True)[:3]
        recommendations = []
        recommendations.append(awslist[top3Services[0]])
        recommendations.append(awslist[top3Services[1]])
        recommendations.append(awslist[top3Services[2]])

        currentFile = os.listdir('./icons/cost-jsons')
        for serviceName in recommendations:
            serviceCode = serviceNameToCodeMapping[serviceName]
            fileName = serviceCode +'.json'
            jsonPath = './icons/cost-jsons/'
            if fileName in currentFile:
                print("File Found")
                self.parseJson(jsonPath+fileName)
            else:
                print("File Download in Progress")
                download_file = subprocess.run(["bash","./get_service_cost.sh",serviceCode])
                #if download_file.resultcode==0:
                #    print("File Downloaded Successfully!")
                #else:
                #    print("File Downloaded Failed!")
                self.parseJson(jsonPath+fileName)
        # print(recommendations)
        self.formGroupBox.close()

    def getCostFromSku(self,filePath,sku):
        with open(filePath,'r') as file:
            data = json.load(file)

        terms =  data['terms']
        onDemandInstances = terms['OnDemand']

        reply=""
        cost=0
        for k1,v1 in onDemandInstances.items():
            for k2,v2 in v1.items():
                for k3,v3 in v2['priceDimensions'].items():
                    if(sku == k1):
                        cost = v3['pricePerUnit']['USD']
                        billingUnit = v3['unit']
                        reply = "Cost: "+v3['pricePerUnit']['USD']+" USD"+" per "+ v3['unit']
                        print("sku",sku)
                        print("description:",v3['description'])
                        print("Cost:",v3['pricePerUnit']['USD'],"USD")
                        print("Billing Unit:",v3['unit'])
                        break
        return float(cost)
        #self.layout.addRow(QLabel("Cost ::"+cost))# $     Latency :: 123 secs"))
        #qm = QMessageBox()
        #qm.setWindowTitle("Service Cost")
        #qm.setWindowIcon(QtGui.QIcon('mapleicon.png'))
        #if(reply==""):
        #    qm.setText("No such configuration found to determine cost")
        #else:
        #    qm.setText(reply)
        #qm.exec_()

    def parseJson(self,filePath):
        with open(filePath,'r') as file:
            data = json.load(file)

        sku_list = []
        products = data['products']
        for k,v in products.items():
            vals = products[k]
            attributes = vals['attributes']
            sku_list.append(k)

        terms =  data['terms']
        onDemandInstances = terms['OnDemand']

        for k1,v1 in onDemandInstances.items():
            for k2,v2 in v1.items():
                for k3,v3 in v2['priceDimensions'].items():
                    if(sku_list[0] == k1):
                        print("sku",sku_list[0])
                        print("description:",v3['description'])
                        print("Cost:",v3['pricePerUnit']['USD'],"USD")
                        print("Billing Unit:",v3['unit'])

    global replaceServicemain
    def displayCompare(self):
        global replaceService
        #global replaceServicemain
        global radiobgcp
        global radiobazure
        replaceServicemain = ""
        self.formGroupBox = QGroupBox()
        self.formGroupBox.setWindowTitle("Comparison")
        self.formGroupBox.setWindowIcon(QtGui.QIcon('mapleicon.png'))
        self.layout = QFormLayout()

        replaceService = self.compareList.split("^")
        param = self.compareList.split("^")
        #for param in self.compareList.split("^"):
        #self.
        radiobgcp = QRadioButton(param[0]+" ")
        radiobgcp.setChecked(True)
        radiobgcp.toggled.connect(self.onClicked)
        rownum= 0
        for iterator in gcplist:
            rownum += 1
            if param[0] == iterator:
                shape = Shape("GCP",100,50,0,0,rownum)
                print(shape.Cost)
                print(shape.Latency)
        self.detail_box = QLabel("Cost : "+str(shape.Cost)+" Latency : "+str(shape.Latency))
        self.layout.addRow(radiobgcp,self.detail_box)
        #self.layout.addWidget(self.label)
        #self.layout.addWidget(self.text_box)

        #self.
        radiobazure = QRadioButton(param[1]+" ")
        #radiobazure.toggled.connect(self.onClicked)
        #self.radiob.toggled.connect(self.onClicked)
        rownum= 0
        for iterator in azurelist:
            rownum += 1
            if param[1] == iterator:
                shape = Shape("AZR",100,50,0,0,rownum)
                print(shape.Cost)
                print(shape.Latency)
        self.detail_box = QLabel("Cost : "+str(shape.Cost)+" Latency : "+str(shape.Latency))
        self.layout.addRow(radiobazure,self.detail_box)


        self.replace_button = QPushButton("Replace")
        self.layout.addRow(self.replace_button)
        self.formGroupBox.setLayout(self.layout)
        self.formGroupBox.show()

        self.replace_button.clicked.connect(lambda: self.replaceBy(replaceServicemain,"GCP"))

    def onClicked(self):
        #radioButton = self.sender()
        #if radioButton.isChecked():
        if radiobgcp.isChecked():
            print(replaceService[0])
            replaceServicemain = replaceService[0]
        elif radiobazure.isChecked():
            print(replaceService[1])
            replaceServicemain = replaceService[1]


    def displayCustPerf(self):
        self.formGroupBox = QGroupBox()
        self.formGroupBox.setWindowTitle("Custom Performance")
        self.formGroupBox.setWindowIcon(QtGui.QIcon('mapleicon.png'))
        self.layout = QFormLayout()

        for param in self.paramList.split("^"):
            self.label = QLabel(param+" ")
            self.text_box = QLineEdit()
            self.layout.addRow(self.label,self.text_box)
            #self.layout.addWidget(self.label)
            #self.layout.addWidget(self.text_box)

        self.submit_button = QPushButton("Submit")
        self.layout.addRow(self.submit_button)
        self.formGroupBox.setLayout(self.layout)
        self.formGroupBox.show()

        self.submit_button.clicked.connect(self.calcCustom)

    def calcCustom(self):
        calculatedCost = 20
        result_text = "Cost Based on Current Configurations is " + str(calculatedCost) + " USD."
        msg = QMessageBox()
        msg.setWindowTitle("Calculated Cost")
        msg.setText(result_text)
        x = msg.exec_()

    def displayDefaultCost(self):
        msg = QMessageBox()
        msg.setWindowTitle("Default Cost")
        msg.setText("Message: " + self.Data.split('^')[6])
        x = msg.exec_()

    def replaceBy(self,ServiceName,CSPname):
        #print("Insid replace "+ServiceName)
        if radiobgcp.isChecked():
            print(replaceService[0])
            ServiceName = replaceService[0]
            CSPname = "GCP"
        elif radiobazure.isChecked():
            print(replaceService[1])
            ServiceName = replaceService[1]
            CSPname = "AZR"

        #print("Insid replace ----- "+replaceServicemain)
        ptX = self.x()
        ptY = self.y()
        print(str(self.x()))
        print(str(self.y()))
        #self.scene.addItem(self.shape)
        #self.shape.setPos(QPointF(position.x(), position.y()))
        #cswid = importlib.import_module("CSWidgets")
        #cswid.widget5.clear()
        #cswid.widget5.addItem(serviceName)
        rownum = 0
        cswid = importlib.import_module("CSWidgets")
        items = cswid.diagram.scene.selectedItems()
        for item in items:
            cswid.diagram.scene.removeItem(item)
        if CSPname == "CSP":
            for iterator in awslist:
                rownum += 1
                if ServiceName == iterator:#.split('^')[0]:
                    #shape = Shape("CSP",100,50,0,0,rownum)
                    shape = Shape(CSPname,100,50,0,0,rownum)
                    shape.setPos(QPointF(ptX, ptY))
                    cswid.diagram.addObject(shape)
                    cswid.diagram.scene.addItem(shape)
        elif CSPname == "GCP":
            for iterator in gcplist:
                rownum += 1
                if ServiceName == iterator:#.split('^')[0]:
                    #shape = Shape("CSP",100,50,0,0,rownum)
                    shape = Shape(CSPname,100,50,0,0,rownum)
                    shape.setPos(QPointF(ptX, ptY))
                    cswid.diagram.addObject(shape)
                    cswid.diagram.scene.addItem(shape)
        elif CSPname == "AZR":
            for iterator in azurelist:
                rownum += 1
                if ServiceName == iterator:#.split('^')[0]:
                    #shape = Shape("CSP",100,50,0,0,rownum)
                    shape = Shape(CSPname,100,50,0,0,rownum)
                    shape.setPos(QPointF(ptX, ptY))
                    cswid.diagram.addObject(shape)
                    cswid.diagram.scene.addItem(shape)

        for iterator in azurelist:
            rownum += 1
            if ServiceName == iterator:#.split('^')[0]:
                #shape = Shape("CSP",100,50,0,0,rownum)
                shape = Shape(CSPname,100,50,0,0,rownum)
                shape.setPos(QPointF(ptX, ptY))
                cswid.diagram.addObject(shape)
                cswid.diagram.scene.addItem(shape)

        #cswid.diagram.calcCostLatency()
        self.formGroupBox.close()


    def replaceByOpt(self,newServiceName,CSPname,newlatency,newcost):
        #print("Insid replace "+ServiceName)
        '''
        if radiobgcp.isChecked():
            print(replaceService[0])
            ServiceName = replaceService[0]
            CSPname = "GCP"
        elif radiobazure.isChecked():
            print(replaceService[1])
            ServiceName = replaceService[1]
            CSPname = "AZR"
            '''
        #print("Insid replace ----- "+replaceServicemain)
        ptX = self.x()
        ptY = self.y()
        print(str(self.x()))
        print(str(self.y()))
        #self.scene.addItem(self.shape)
        #self.shape.setPos(QPointF(position.x(), position.y()))
        #cswid = importlib.import_module("CSWidgets")
        #cswid.widget5.clear()
        #cswid.widget5.addItem(serviceName)
        rownum = 0
        cswid = importlib.import_module("CSWidgets")
        #items = cswid.diagram.scene.selectedItems()
        #for item in items:
        print("-----------------------------")
        print(self)
        print(newServiceName)
        print("-----------------------------")
        #awslist.append("Amazon DynamoDB Accelerator (DAX)")
        cswid.diagram.scene.removeItem(self)
        for item in cswid.diagram.scene.items():
            if isinstance(item,QGraphicsTextItem) and item.toPlainText()==self.name:
                print(item)
                cswid.diagram.scene.removeItem(item)
        shape=0
        if CSPname == "CSP":
            for iterator in awslist:
                rownum += 1
                if newServiceName == iterator:#.split('^')[0]:
                    #shape = Shape("CSP",100,50,0,0,rownum)
                    print("changgggggggggggggggggggggggggggggggggggggggggggggggggggggge")
                    shape = Shape(CSPname,100,50,newlatency,newcost,rownum)
                    shape.Latency=newlatency
                    shape.Cost=newcost
                    shape.setPos(QPointF(ptX, ptY))
                    cswid.diagram.addObject(shape)
                    cswid.diagram.scene.addItem(shape)
                    text_item=QGraphicsTextItem(newServiceName)
                    text_item.setDefaultTextColor(QColor(Qt.black))
                    shape.textItem = text_item
                    # print(shapeObject.textItem)
                    text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
                    text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
                    text_item.setPos(QPointF(ptX,ptY+55))
                    font = QFont()
                    font.setPointSize(15)  # Increase the font size to 16

                    # Apply the font to the QGraphicsTextItem
                    text_item.setFont(font)
                    cswid.diagram.scene.addItem(text_item)
                    print("name putttttttttttttttttttttttttttttt")
        elif CSPname == "GCP":
            for iterator in gcplist:
                rownum += 1
                if newServiceName == iterator:#.split('^')[0]:
                    #shape = Shape("CSP",100,50,0,0,rownum)
                    shape = Shape(CSPname,100,50,0,0,rownum)
                    shape.setPos(QPointF(ptX, ptY))
                    cswid.diagram.addObject(shape)
                    cswid.diagram.scene.addItem(shape)
        elif CSPname == "AZR":
            for iterator in azurelist:
                rownum += 1
                if newServiceName == iterator:#.split('^')[0]:
                    #shape = Shape("CSP",100,50,0,0,rownum)
                    shape = Shape(CSPname,100,50,0,0,rownum)
                    shape.setPos(QPointF(ptX, ptY))
                    cswid.diagram.addObject(shape)
                    cswid.diagram.scene.addItem(shape)

        for iterator in azurelist:
            rownum += 1
            if newServiceName == iterator:#.split('^')[0]:
                #shape = Shape("CSP",100,50,0,0,rownum)
                shape = Shape(CSPname,100,50,0,0,rownum)
                shape.setPos(QPointF(ptX, ptY))
                cswid.diagram.addObject(shape)
                cswid.diagram.scene.addItem(shape)

        #cswid.diagram.calcCostLatency()
        #self.formGroupBox.close()
        return shape

    def addOpt(self,newServiceName,CSPname,newlatency,newcost,dir,otherserv):
        print("Insid replace "+newServiceName)
        '''
        if radiobgcp.isChecked():
            print(replaceService[0])
            ServiceName = replaceService[0]
            CSPname = "GCP"
        elif radiobazure.isChecked():
            print(replaceService[1])
            ServiceName = replaceService[1]
            CSPname = "AZR"
            '''
        #print("Insid replace ----- "+replaceServicemain)
        ptX = self.x()
        ptY = self.y()
        print(str(self.x()))
        print(str(self.y()))
        #self.scene.addItem(self.shape)
        #self.shape.setPos(QPointF(position.x(), position.y()))
        #cswid = importlib.import_module("CSWidgets")
        #cswid.widget5.clear()
        #cswid.widget5.addItem(serviceName)
        rownum = 0
        cswid = importlib.import_module("CSWidgets")
        #items = cswid.diagram.scene.selectedItems()
        #for item in items:
        print("-----------------------------")
        print(self)
        print(newServiceName)
        print("-----------------------------")
        #awslist.append("Amazon DynamoDB Accelerator (DAX)")
        #cswid.diagram.scene.removeItem(self)
        #for item in cswid.diagram.scene.items():
        #    if isinstance(item,QGraphicsTextItem) and item.toPlainText()==self.name:
        #        print(item)
        #        cswid.diagram.scene.removeItem(item)
        shape=0
        if CSPname == "CSP":
            for iterator in awslist:
                rownum += 1
                if newServiceName == iterator:#.split('^')[0]:
                    #shape = Shape("CSP",100,50,0,0,rownum)
                    shape = Shape(CSPname,100,50,newlatency,newcost,rownum)
                    shape.Latency=newlatency
                    shape.Cost=newcost
                    shape.setPos(QPointF(ptX, ptY))
                    cswid.diagram.addObject(shape)
                    cswid.diagram.scene.addItem(shape)
                    text_item=QGraphicsTextItem(newServiceName)
                    text_item.setDefaultTextColor(QColor(Qt.black))
                    text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
                    text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
                    text_item.setPos(QPointF(ptX,ptY+55))
                    shape.textItem = text_item
                    font = QFont()
                    font.setPointSize(15)  # Increase the font size to 16

                    # Apply the font to the QGraphicsTextItem
                    text_item.setFont(font)
                    cswid.diagram.scene.addItem(text_item)

                    if (dir=="from" or dir=="both"):
                        for ii in self.out_arrows:
                            cswid.diagram.scene.removeItem(ii)
                            cswid.diagram.addArrowopt(shape,otherserv,False)
                        #cswid.diagram.addArrowopt(self,shape,False)
                    if (dir=="to" or dir=="both"):
                        for ii in self.in_arrows:
                            cswid.diagram.scene.removeItem(ii)
                            cswid.diagram.addArrowopt(otherserv,shape,False)
                        #cswid.diagram.addArrowopt(shape,self,False)
                    #cswid.diagram.addArrowopt(shape,self)
        elif CSPname == "GCP":
            for iterator in gcplist:
                rownum += 1
                if newServiceName == iterator:#.split('^')[0]:
                    #shape = Shape("CSP",100,50,0,0,rownum)
                    shape = Shape(CSPname,100,50,0,0,rownum)
                    shape.setPos(QPointF(ptX, ptY))
                    cswid.diagram.addObject(shape)
                    cswid.diagram.scene.addItem(shape)
        elif CSPname == "AZR":
            for iterator in azurelist:
                rownum += 1
                if newServiceName == iterator:#.split('^')[0]:
                    #shape = Shape("CSP",100,50,0,0,rownum)
                    shape = Shape(CSPname,100,50,0,0,rownum)
                    shape.setPos(QPointF(ptX, ptY))
                    cswid.diagram.addObject(shape)
                    cswid.diagram.scene.addItem(shape)

        for iterator in azurelist:
            rownum += 1
            if newServiceName == iterator:#.split('^')[0]:
                #shape = Shape("CSP",100,50,0,0,rownum)
                shape = Shape(CSPname,100,50,0,0,rownum)
                shape.setPos(QPointF(ptX, ptY))
                cswid.diagram.addObject(shape)
                cswid.diagram.scene.addItem(shape)

        #cswid.diagram.calcCostLatency()
        #self.formGroupBox.close()
        return shape



    def selectNext(self, serviceName):
        print(serviceName)
        cswid = importlib.import_module("CSWidgets")
        cswid.widget5.clear()
        cswid.widget5.addItem(serviceName)
        print("serviceName "+serviceName)
        #for row in worksheet.iter_rows(min_col=1, max_col=1, values_only=True):
        #    if value_to_find in row:
        #        serviceName = row[0]
        #        break
        #for iterator in awslist:
        #    print(iterator.split('^')[0])
        #    if  serviceName == iterator.split('^')[0]:
        #        print("Received "+iterator)
        #        cswid.widget5.addItem(serviceName)

    def displayCustCost(self):
        self.formGroupBox = QGroupBox()
        self.formGroupBox.setWindowTitle("Custom Cost")
        self.formGroupBox.setWindowIcon(QtGui.QIcon('mapleicon.png'))
        self.layout = QFormLayout()

        for k,v in self.attributes.items():
            label = QLabel(k)
            text_box = QLineEdit()
            text_box.setText(v)
            text_box.setFixedWidth(300)
            self.layout.addRow(label,text_box)
            #self.layout.addWidget(self.label)
            #self.layout.addWidget(self.text_box)

        #self.submit_button = QPushButton("Submit")
        #self.layout.addRow(self.submit_button)

        self.submit_button = QPushButton("Submit")
        self.layout.addRow(self.submit_button)
        self.formGroupBox.setLayout(self.layout)
        self.submit_button.clicked.connect(self.displayCustCostSubmit)
        self.formGroupBox.show()

    def getSkuAttributesMapping(self,filePath):
        with open(filePath,'r') as file:
            data = json.load(file)
        skuAttributesMap = []
        products = data['products']
        for k,v in products.items():
            vals = products[k]
            sku = vals['sku']
            attributes = vals['attributes']
            temp_dict = {}
            temp_dict[sku] = attributes
            skuAttributesMap.append(temp_dict)
        return skuAttributesMap

    # def getSkuInstanceTypeMapping(self,filePath):
    #     with open(filePath,'r') as file:
    #         data = json.load(file)
    #     skuInstanceTypeMap = {}
    #     products = data['products']
    #     for k,v in products.items():
    #         sku = vals['sku']
    #         attributes = vals['attributes']
    #         instanceType = attributes['instanceType']
    #         skuInstanceTypeMap.getOrDefault

    def getSkuFromAttributes(self,attrbutesDict, list_of_dicts):
        for index, d in enumerate(list_of_dicts):
            if attrbutesDict == list(d.values())[0]:
                return list(list_of_dicts[index].keys())[0]
        return 0

    # def getSkuFromInstanceType(self,instanceType):
    #     return 0


    def azureGetCostFromSku(self,fileName,skuId):
        with open(fileName) as f:
            data = json.load(f)
        for item in data["Items"]:
            if item["skuId"] == skuId:
                cost = item["unitPrice"]
                #cost_unit = data["currencyCode"]
                return cost
        return "Not Found"

    def displayCustCostSubmit(self):
        custPerfCost = 0
        for i in range(self.layout.count()):
            item = self.layout.itemAt(i)
            print(type(item))
            if item and isinstance(item.widget(), QComboBox):
                key = self.layout.labelForField(item.widget()).text()
                value = item.widget().currentText()
                print(self.name+" - Key :"+key+" Value : "+value)
                self.attributes[key] = value
            elif item and isinstance(item.widget(),QLineEdit):
                val = item.widget().text()
                if val=="": val = 0
                else: val = float(val)
                custPerfCost += val
            elif item:
                print(str(type(item))+" IF ITEM "+self.name)
            else:
                print("only ELSE "+self.name)

        #print("Updated attributes:", self.attributes)
        serviceName = self.name
        serviceCode = serviceNameToCodeMapping[serviceName]
        fileName = serviceCode +'.json'
        jsonPath = './icons/cost-jsons/'
        currentFile = os.listdir('./icons/cost-jsons')
        if(self.text=='AZR'):
            print("Azure:",self.attributes['skuId'])
            cost = self.azureGetCostFromSku(jsonPath+fileName,self.attributes['skuId'])
            self.Cost = cost
            lbl = QLabel()
            lbl.setText("<br><font color='#ffa500',font size='4'>Cost :: "+str(self.Cost)+" USD<br>Latency :: "+str(self.Latency)+" secs"+"</font>")
            #self.layout.addRow(QLabel("Cost :: "+str(cost)+" USD"))
            self.layout.addRow(lbl)
            return
        list_dicts = []
        sku=""
        if fileName in currentFile:
            print("File Found")
            list_dicts = self.getSkuAttributesMapping(jsonPath+fileName)
            sku = self.getSkuFromAttributes(self.attributes,list_dicts)
        else:
            print("File Download in Progress")
            download_file = subprocess.run(["bash","./get_service_cost.sh",serviceCode])
            #if download_file.resultcode==0:
            #    print("File Downloaded Successfully!")
            #else:
            #    print("File Downloaded Failed!")
            list_dicts = self.getSkuAttributesMapping(jsonPath+fileName)
            sku = self.getSkuFromAttributes(self.attributes,list_dicts)
        print(sku)
        if(sku!=0):
            cost = self.getCostFromSku(jsonPath+fileName,sku) * custPerfCost
            self.Cost = cost
            self.Latency = 0.8
            # print("Cost------------",cost)
            lbl = QLabel()
            lbl.setText("<br><font color='#ffa500',font size='4'>Cost :: "+str(self.Cost)+" USD<br>Latency :: "+str(self.Latency)+" secs"+"</font>")
            #self.layout.addRow(QLabel("Cost :: "+str(cost)+" USD"))
            self.layout.addRow(lbl)
            #self.formGroupBox.close()
        else:
            msg = QMessageBox()
            msg.setWindowTitle("Saved Message")
            msg.setText("Inavlid Configuration, No SKU found")
            x = msg.exec_()

    def getAtrributesFromJson(self,filePath):
        with open(filePath,'r') as file:
            data = json.load(file)
        products = data['products']
        for k,v in products.items():
            vals = products[k]
            attributes = vals['attributes']
            break
        return attributes


    def addTfr(self,tfr): #add transformer details of selected transformer to shape
        attrs = vars(tfr)
        self.Cost = attrs["Cost"]
        self.Latency=  attrs["Latency"]
        self.Type1 = attrs["Type"]

    def displayDetails(self):
        msg = QMessageBox()
        msg.setWindowTitle("Transformer Details")
        msg.setText("Cost: " + str(self.Cost) + "$\nLatency: " + str(self.Latency) + " ms\nType: " + self.Type1)
        x = msg.exec_()

    def __del__(self):
        # Clean up service name text
        if hasattr(self, 'serviceNameText') and self.serviceNameText is not None:
            self.serviceNameText.setParentItem(None)
            self.serviceNameText = None

        for arrow in self.in_arrows:
            self.in_arrows.remove(arrow)
        for arrow in self.out_arrows:
            self.out_arrows.remove(arrow)

    def addInLink(self, arrow):
        self.in_arrows.append(arrow)

    def addOutLink(self,arrow):
        self.out_arrows.append(arrow)

    def itemChange(self,change,value): #update position of arrows
        if change == self.ItemPositionChange:
            # Update arrows
            for arrow in self.in_arrows:
                arrow.trackNodes()
            for arrow in self.out_arrows:
                arrow.trackNodes()

            # Update textItem position (service icon) if it's a child item
            if hasattr(self, 'textItem') and self.textItem is not None and isinstance(self.textItem, QGraphicsPixmapItem):
                rect = self.textItem.boundingRect()
                rect.moveCenter(self.boundingRect().center())
                self.textItem.setPos(rect.topLeft())

            # Update service name text position - now handled automatically since it's a child item
            if hasattr(self, 'serviceNameText') and self.serviceNameText is not None:
                # Position the text below the service icon (relative to parent Shape)
                self.serviceNameText.setPos(QPointF(0, 55))

            # Update proxy position (checkbox)
            if hasattr(self, 'proxy') and self.proxy is not None:
                if hasattr(self, 'textItem') and self.textItem is not None and isinstance(self.textItem, QGraphicsPixmapItem):
                    # Position proxy relative to textItem
                    self.proxy.setPos(self.textItem.pos() - QPointF(5, 0))
                else:
                    # Position proxy relative to shape if textItem doesn't exist
                    rect = self.boundingRect()
                    self.proxy.setPos(rect.topLeft() - QPointF(5, 0))

            # Notify diagram to update VPC boxes if this service is inside a VPC
            cswid = importlib.import_module("CSWidgets")
            if hasattr(cswid, 'diagram') and cswid.diagram is not None:
                cswid.diagram.updateVPCBoxes(self)

        if (change == self.ItemSelectedChange):
            self.selectedTime = time.time()

            # Also select the service name text when the shape is selected
            if hasattr(self, 'serviceNameText') and self.serviceNameText is not None:
                self.serviceNameText.setSelected(self.isSelected())

        return super(Shape, self).itemChange(change, value)

    def pos1(self): #centre of object
        xOffset = self.rect().x() + self.rect().width()/2
        yOffset = self.rect().y() + self.rect().height()/2
        return QPointF(self.pos().x() + xOffset, self.pos().y()+yOffset)

    def getList(self): #return relevant attributes for saving
        self.list = [self.text,self.a,self.b,self.Cost,self.Latency,self.Type1]
        str = ["Shape",self.scenePos().x(),self.scenePos().y()]
        str += self.list
        return str

    def getLeftEdgePosition(self):
        yOffset = self.rect().y() + self.rect().height()/2
        return QPointF(self.pos().x(), self.pos().y()+yOffset)

    def getRightEdgePosition(self):
        yOffset = self.rect().y() + self.rect().height()/2
        xOffset = self.rect().x() + self.rect().width()
        return QPointF(self.pos().x()+xOffset , self.pos().y()+yOffset)

    def getTopEdgePosition(self):
        xOffset = self.rect().x() + self.rect().width()/2
        return QPointF(self.pos().x()+xOffset , self.pos().y())

    def getBottomEdgePosition(self):
        xOffset = self.rect().x() + self.rect().width()/2
        yOffset = self.rect().y() + self.rect().height()
        return QPointF(self.pos().x()+xOffset , self.pos().y()+yOffset)





