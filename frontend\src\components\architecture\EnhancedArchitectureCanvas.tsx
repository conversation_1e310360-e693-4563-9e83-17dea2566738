import React, { useRef, useState, useCallback, useEffect } from 'react'
import React<PERSON>low, {
  ReactFlowProvider,
  ReactFlowInstance,
  Node,
  Edge,
  OnNodesChange,
  OnEdgesChange,
  OnConnect,
  Background,
  BackgroundVariant,
  Controls,
  MiniMap,
  ConnectionMode,
  useReactFlow
} from 'reactflow'
import 'reactflow/dist/style.css'

import { ArchitectureNode, ArchitectureEdge, CloudService } from '@/types/architecture'
import { useToast } from '@/hooks/use-toast'
import ServiceNode from './nodes/ServiceNode'
import UserNode from './nodes/UserNode'
import DirectedEdge from './edges/DirectedEdge'
import EnhancedStepEdge from './edges/EnhancedStepEdge'

// Node types for React Flow
const nodeTypes = {
  service: ServiceNode,
  user: UserNode,
}

// Edge types for React Flow with enhanced arrow visualization
const edgeTypes = {
  directed: DirectedEdge,
  enhancedStep: EnhancedStepEdge,
  step: EnhancedStepEdge, // Use enhanced step as default
}

interface EnhancedArchitectureCanvasProps {
  nodes: ArchitectureNode[]
  edges: ArchitectureEdge[]
  onNodesChange: OnNodesChange
  onEdgesChange: OnEdgesChange
  onConnect: OnConnect
  onNodeSelect: (node: ArchitectureNode | null) => void
  onAddNode: (service: CloudService, position: { x: number; y: number }) => void
  className?: string
  triggerFitView?: boolean
  optimizedForLoadImage?: boolean
}

export const EnhancedArchitectureCanvas: React.FC<EnhancedArchitectureCanvasProps> = ({
  nodes,
  edges,
  onNodesChange,
  onEdgesChange,
  onConnect,
  onNodeSelect,
  onAddNode,
  className,
  triggerFitView,
  optimizedForLoadImage = false
}) => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null)
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null)
  const { toast } = useToast()

  // Convert architecture nodes to React Flow nodes
  const reactFlowNodes: Node[] = nodes.map(node => ({
    id: node.id,
    type: node.type,
    position: node.position,
    data: node.data,
    selected: node.selected,
    draggable: true,
    selectable: true,
    style: {
      width: node.data.service.id === 'user' ? 80 : 120,
      height: node.data.service.id === 'user' ? 80 : 100,
    }
  }))

  // Convert architecture edges to React Flow edges
  const reactFlowEdges: Edge[] = edges.map(edge => ({
    id: edge.id,
    source: edge.source,
    target: edge.target,
    type: edge.type || 'enhancedStep',
    animated: edge.animated || false,
    style: edge.style || {
      stroke: '#2563EB',
      strokeWidth: 3,
      strokeDasharray: '0',
      strokeLinecap: 'round',
      strokeLinejoin: 'round'
    },
    markerEnd: edge.markerEnd || {
      type: 'arrowclosed',
      color: '#2563EB',
      width: 26,
      height: 26
    },
    data: edge.data || {
      showDirection: true,
      strokeColor: '#2563EB',
      strokeWidth: 3
    }
  }))

  const onInit = useCallback((reactFlowInstance: ReactFlowInstance) => {
    console.log('Enhanced React Flow initialized')
    setReactFlowInstance(reactFlowInstance)
  }, [])

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'move'
  }, [])

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault()

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect()
      if (!reactFlowBounds || !reactFlowInstance) return

      const serviceData = event.dataTransfer.getData('application/reactflow')
      if (!serviceData) return

      try {
        const service: CloudService = JSON.parse(serviceData)
        
        const position = reactFlowInstance.project({
          x: event.clientX - reactFlowBounds.left,
          y: event.clientY - reactFlowBounds.top,
        })

        console.log('Dropping service at position:', position, 'Service:', service.name)
        onAddNode(service, position)

        toast({
          title: "Service Added",
          description: `${service.name} has been added to your architecture.`,
        })
      } catch (error) {
        console.error('Error parsing dropped service data:', error)
        toast({
          title: "Error",
          description: "Failed to add service to architecture.",
          variant: "destructive",
        })
      }
    },
    [reactFlowInstance, onAddNode, toast]
  )

  const onNodeClick = useCallback(
    (event: React.MouseEvent, node: Node) => {
      console.log('Node clicked:', node.id)
      const architectureNode = nodes.find(n => n.id === node.id)
      if (architectureNode) {
        onNodeSelect(architectureNode)
      }
    },
    [nodes, onNodeSelect]
  )

  const onEdgeClick = useCallback(
    (event: React.MouseEvent, edge: Edge) => {
      console.log('Edge clicked:', edge.id)
    },
    []
  )

  const onPaneClick = useCallback(() => {
    console.log('Pane clicked - deselecting nodes')
    onNodeSelect(null)
  }, [onNodeSelect])

  // Enhanced fit view with better settings for Load Image
  useEffect(() => {
    if (triggerFitView && reactFlowInstance && nodes.length > 0) {
      console.log('Triggering enhanced fit view for', nodes.length, 'nodes')
      
      // Use different settings based on whether this is optimized for Load Image
      const fitViewOptions = optimizedForLoadImage ? {
        padding: 0.15, // Less padding for Load Image to maximize space usage
        includeHiddenNodes: false,
        minZoom: 0.3,   // Higher minimum zoom for better visibility
        maxZoom: 1.5,   // Reasonable maximum zoom
        duration: 800   // Smooth animation
      } : {
        padding: 0.2,
        includeHiddenNodes: false,
        minZoom: 0.2,
        maxZoom: 2.0,
        duration: 600
      }

      setTimeout(() => {
        reactFlowInstance.fitView(fitViewOptions)
      }, 100)
    }
  }, [triggerFitView, reactFlowInstance, nodes.length, optimizedForLoadImage])

  // Enhanced default settings
  const defaultZoom = optimizedForLoadImage ? 0.5 : 0.4 // Higher default zoom
  const minZoom = optimizedForLoadImage ? 0.3 : 0.1
  const maxZoom = optimizedForLoadImage ? 1.5 : 2.0

  return (
    <div className={className} ref={reactFlowWrapper}>
      <ReactFlow
        nodes={reactFlowNodes}
        edges={reactFlowEdges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onInit={onInit}
        onDrop={onDrop}
        onDragOver={onDragOver}
        onNodeClick={onNodeClick}
        onEdgeClick={onEdgeClick}
        onPaneClick={onPaneClick}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        connectionMode={ConnectionMode.Strict}
        defaultEdgeOptions={{
          type: 'enhancedStep',
          markerEnd: {
            type: 'arrowclosed',
            color: '#2563EB',
            width: 26,
            height: 26
          },
          style: {
            stroke: '#2563EB',
            strokeWidth: 3,
            strokeDasharray: '0',
            strokeLinecap: 'round',
            strokeLinejoin: 'round'
          },
          data: {
            showDirection: true,
            strokeColor: '#2563EB',
            strokeWidth: 3
          }
        }}
        fitView
        fitViewOptions={{
          padding: optimizedForLoadImage ? 0.15 : 0.2,
          includeHiddenNodes: false,
          minZoom: minZoom,
          maxZoom: maxZoom
        }}
        minZoom={minZoom}
        maxZoom={maxZoom}
        defaultZoom={defaultZoom}
        defaultViewport={{ x: 0, y: 0, zoom: defaultZoom }}
        attributionPosition="bottom-left"
        proOptions={{ hideAttribution: true }}
      >
        <Background
          variant={BackgroundVariant.Dots}
          gap={25}
          size={1.2}
          color="#e5e5e5"
        />
        <Controls
          position="top-left"
          showZoom={true}
          showFitView={true}
          showInteractive={true}
          style={{
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            border: '1px solid #e5e5e5',
            borderRadius: '8px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}
        />
        <MiniMap
          position="bottom-right"
          nodeColor={(node) => {
            const architectureNode = nodes.find(n => n.id === node.id)
            if (architectureNode?.data.service.provider === 'AWS') return '#FF9900'
            if (architectureNode?.data.service.provider === 'GCP') return '#4285F4'
            if (architectureNode?.data.service.provider === 'Azure') return '#0078D4'
            return '#6b7280'
          }}
          maskColor="rgba(0, 0, 0, 0.05)"
          style={{
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            border: '1px solid #e5e5e5',
            borderRadius: '8px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}
        />
      </ReactFlow>
    </div>
  )
}

// Wrapper component with ReactFlowProvider
export const EnhancedArchitectureCanvasWrapper: React.FC<EnhancedArchitectureCanvasProps> = (props) => {
  return (
    <ReactFlowProvider>
      <EnhancedArchitectureCanvas {...props} />
    </ReactFlowProvider>
  )
}

export default EnhancedArchitectureCanvasWrapper
