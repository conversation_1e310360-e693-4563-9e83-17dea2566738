import { ArchitectureTemplate, Architecture, ArchitectureNode } from '@/types/architecture'
import { getServiceById } from './serviceDefinitions'

// Helper function to create nodes with React Flow properties
const createNode = (nodeData: Partial<ArchitectureNode>): ArchitectureNode => ({
  selected: false,
  dragging: false,
  width: 48,  // Compact icon-only nodes (w-12 in Tailwind)
  height: 48, // Compact icon-only nodes (h-12 in Tailwind)
  zIndex: 0,
  hidden: false,
  deletable: true,
  selectable: true,
  connectable: true,
  focusable: true,
  ...nodeData
} as ArchitectureNode)

// Serverless Web Application Template
const serverlessWebAppTemplate: Architecture = {
  name: 'Serverless Web Application',
  description: 'A scalable serverless web application using AWS Lambda, API Gateway, and DynamoDB',
  nodes: [
    createNode({
      id: 'user-1',
      type: 'userNode',
      position: { x: 100, y: 100 },
      data: {
        service: {
          id: 'user',
          name: 'Web Users',
          provider: 'AWS' as const,
          category: 'Compute' as const,
          icon: '👤',
          description: 'End users accessing the web application',
          defaultConfig: {},
          costModel: '',
          latencyModel: '',
          color: '#8B5CF6'
        },
        config: { userCount: 10000, location: 'Global' },
        label: 'Web Users'
      }
    }),
    createNode({
      id: 'cloudfront-1',
      type: 'awsService',
      position: { x: 300, y: 100 },
      data: {
        service: getServiceById('aws-cloudfront')!,
        config: { dataTransfer: 1000, requests: 1000000 },
        label: 'CloudFront CDN'
      }
    }),
    createNode({
      id: 'apigateway-1',
      type: 'awsService',
      position: { x: 500, y: 100 },
      data: {
        service: getServiceById('aws-api-gateway')!,
        config: { type: 'HTTP', requests: 1000000 },
        label: 'API Gateway'
      }
    }),
    createNode({
      id: 'lambda-1',
      type: 'awsService',
      position: { x: 700, y: 100 },
      data: {
        service: getServiceById('aws-lambda')!,
        config: { memory: 1024, timeout: 30, workload: 100000 },
        label: 'Lambda Function'
      }
    }),
    createNode({
      id: 'dynamodb-1',
      type: 'awsService',
      position: { x: 700, y: 300 },
      data: {
        service: getServiceById('aws-dynamodb')!,
        config: { billingMode: 'On-Demand', workload: 50000 },
        label: 'DynamoDB'
      }
    }),
    createNode({
      id: 's3-1',
      type: 'awsService',
      position: { x: 500, y: 300 },
      data: {
        service: getServiceById('aws-s3')!,
        config: { storageClass: 'Standard', workload: 10000, fileSize: 1024 },
        label: 'S3 Storage'
      }
    })
  ],
  edges: [
    {
      id: 'edge-1',
      source: 'user-1',
      target: 'cloudfront-1',
      type: 'default',
      animated: true
    },
    {
      id: 'edge-2',
      source: 'cloudfront-1',
      target: 'apigateway-1',
      type: 'default'
    },
    {
      id: 'edge-3',
      source: 'apigateway-1',
      target: 'lambda-1',
      type: 'default'
    },
    {
      id: 'edge-4',
      source: 'lambda-1',
      target: 'dynamodb-1',
      type: 'default'
    },
    {
      id: 'edge-5',
      source: 'lambda-1',
      target: 's3-1',
      type: 'default'
    }
  ],
  metadata: {
    archType: 'Serverless',
    provider: 'AWS'
  }
}

// Machine Learning Pipeline Template
const mlPipelineTemplate: Architecture = {
  name: 'ML Training Pipeline',
  description: 'End-to-end machine learning pipeline with data processing and model training',
  nodes: [
    createNode({
      id: 's3-data',
      type: 'awsService',
      position: { x: 100, y: 100 },
      data: {
        service: getServiceById('aws-s3')!,
        config: { storageClass: 'Standard', workload: 1000, fileSize: 10240 },
        label: 'Training Data'
      }
    }),
    createNode({
      id: 'sagemaker-1',
      type: 'awsService',
      position: { x: 400, y: 100 },
      data: {
        service: getServiceById('aws-sagemaker')!,
        config: { instanceType: 'ml.m5.2xlarge', component: 'Training' },
        label: 'SageMaker Training'
      }
    }),
    createNode({
      id: 's3-model',
      type: 'awsService',
      position: { x: 700, y: 100 },
      data: {
        service: getServiceById('aws-s3')!,
        config: { storageClass: 'Standard', workload: 100, fileSize: 5120 },
        label: 'Model Artifacts'
      }
    }),
    createNode({
      id: 'sagemaker-endpoint',
      type: 'awsService',
      position: { x: 400, y: 300 },
      data: {
        service: getServiceById('aws-sagemaker')!,
        config: { instanceType: 'ml.m5.large', component: 'Hosting' },
        label: 'SageMaker Endpoint'
      }
    }),
    createNode({
      id: 'lambda-inference',
      type: 'awsService',
      position: { x: 100, y: 300 },
      data: {
        service: getServiceById('aws-lambda')!,
        config: { memory: 2048, timeout: 60, workload: 10000 },
        label: 'Inference Lambda'
      }
    })
  ],
  edges: [
    {
      id: 'edge-1',
      source: 's3-data',
      target: 'sagemaker-1',
      type: 'default'
    },
    {
      id: 'edge-2',
      source: 'sagemaker-1',
      target: 's3-model',
      type: 'default'
    },
    {
      id: 'edge-3',
      source: 's3-model',
      target: 'sagemaker-endpoint',
      type: 'default'
    },
    {
      id: 'edge-4',
      source: 'lambda-inference',
      target: 'sagemaker-endpoint',
      type: 'default'
    }
  ],
  metadata: {
    archType: 'Microservices',
    provider: 'AWS'
  }
}

// Data Analytics Template
const dataAnalyticsTemplate: Architecture = {
  name: 'Real-time Data Analytics',
  description: 'Real-time data processing and analytics pipeline',
  nodes: [
    createNode({
      id: 'kinesis-1',
      type: 'awsService',
      position: { x: 100, y: 200 },
      data: {
        service: getServiceById('aws-kinesis')!,
        config: { shards: 5, retentionPeriod: 24 },
        label: 'Kinesis Stream'
      }
    }),
    createNode({
      id: 'lambda-processor',
      type: 'awsService',
      position: { x: 400, y: 100 },
      data: {
        service: getServiceById('aws-lambda')!,
        config: { memory: 1024, timeout: 300, workload: 100000 },
        label: 'Stream Processor'
      }
    }),
    createNode({
      id: 'dynamodb-realtime',
      type: 'awsService',
      position: { x: 700, y: 100 },
      data: {
        service: getServiceById('aws-dynamodb')!,
        config: { billingMode: 'On-Demand', workload: 50000 },
        label: 'Real-time Data'
      }
    }),
    createNode({
      id: 's3-archive',
      type: 'awsService',
      position: { x: 400, y: 300 },
      data: {
        service: getServiceById('aws-s3')!,
        config: { storageClass: 'Standard-IA', workload: 10000, fileSize: 2048 },
        label: 'Data Archive'
      }
    }),
    createNode({
      id: 'redshift-1',
      type: 'awsService',
      position: { x: 700, y: 300 },
      data: {
        service: getServiceById('aws-redshift')!,
        config: { nodeType: 'dc2.large', numberOfNodes: 2 },
        label: 'Data Warehouse'
      }
    })
  ],
  edges: [
    {
      id: 'edge-1',
      source: 'kinesis-1',
      target: 'lambda-processor',
      type: 'default',
      animated: true
    },
    {
      id: 'edge-2',
      source: 'lambda-processor',
      target: 'dynamodb-realtime',
      type: 'default'
    },
    {
      id: 'edge-3',
      source: 'lambda-processor',
      target: 's3-archive',
      type: 'default'
    },
    {
      id: 'edge-4',
      source: 's3-archive',
      target: 'redshift-1',
      type: 'default'
    }
  ],
  metadata: {
    archType: 'Microservices',
    provider: 'AWS'
  }
}

export const ARCHITECTURE_TEMPLATES: ArchitectureTemplate[] = [
  {
    id: 'serverless-web-app',
    name: 'Serverless Web Application',
    description: 'Scalable serverless web application with CDN, API Gateway, Lambda, and DynamoDB',
    category: 'Web Applications',
    thumbnail: '/templates/serverless-web-app.png',
    architecture: serverlessWebAppTemplate,
    tags: ['serverless', 'web', 'scalable', 'aws']
  },
  {
    id: 'ml-pipeline',
    name: 'ML Training Pipeline',
    description: 'Complete machine learning pipeline for training and deploying models',
    category: 'Machine Learning',
    thumbnail: '/templates/ml-pipeline.png',
    architecture: mlPipelineTemplate,
    tags: ['ml', 'sagemaker', 'training', 'aws']
  },
  {
    id: 'data-analytics',
    name: 'Real-time Data Analytics',
    description: 'Real-time data processing and analytics with Kinesis and Redshift',
    category: 'Data & Analytics',
    thumbnail: '/templates/data-analytics.png',
    architecture: dataAnalyticsTemplate,
    tags: ['analytics', 'real-time', 'kinesis', 'redshift']
  }
]

export const getTemplateById = (id: string): ArchitectureTemplate | undefined => {
  return ARCHITECTURE_TEMPLATES.find(template => template.id === id)
}

export const getTemplatesByCategory = (category: string): ArchitectureTemplate[] => {
  return ARCHITECTURE_TEMPLATES.filter(template => template.category === category)
}
