import React, { memo } from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { Card } from '@/components/ui/card'
import { User } from 'lucide-react'
import { cn } from '@/lib/utils'

interface UserNodeData {
  label: string
  userCount?: number
  location?: string
  selected?: boolean
}

export const UserNode = memo<NodeProps<UserNodeData>>(({ data, selected }) => {
  return (
    <>
      {/* Compact Icon-Only User Node */}
      <div className={cn(
        'w-12 h-12 rounded-lg transition-all duration-200 cursor-pointer flex items-center justify-center border-2 shadow-sm hover:shadow-md',
        'border-purple-400 bg-purple-50 hover:bg-purple-100',
        selected ? 'ring-2 ring-blue-500 shadow-md scale-110' : 'hover:scale-105'
      )}>
        <div className="relative group">
          <User className="w-7 h-7 text-purple-600 drop-shadow-sm" />

          {/* Tooltip on hover */}
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-50 pointer-events-none">
            {data.label || 'Users'}
          </div>
        </div>
      </div>

      {/* Top Handle - Input */}
      <Handle
        type="target"
        position={Position.Top}
        id="top"
        className="w-2 h-2 !bg-purple-400 border border-white"
        style={{ top: -4 }}
      />

      {/* Bottom Handle - Output */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="bottom"
        className="w-2 h-2 !bg-purple-400 border border-white"
        style={{ bottom: -4 }}
      />

      {/* Right Handle - Output */}
      <Handle
        type="source"
        position={Position.Right}
        id="right"
        className="w-2 h-2 !bg-purple-400 border border-white"
        style={{ right: -4 }}
      />

      {/* Left Handle - Input */}
      <Handle
        type="target"
        position={Position.Left}
        id="left"
        className="w-2 h-2 !bg-purple-400 border border-white"
        style={{ left: -4 }}
      />
    </>
  )
})

UserNode.displayName = 'UserNode'


