import React, { useState, useEffect, useCallback } from 'react'
import { useLocation } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { useToast } from '@/hooks/use-toast'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from '@/components/ui/dropdown-menu'

// Enhanced Architecture components
import EnhancedArchitectureCanvas from '@/components/architecture/EnhancedArchitectureCanvas'
import CollapsibleComponentPalette from '@/components/architecture/CollapsibleComponentPalette'
import FloatingPropertiesPanel from '@/components/architecture/FloatingPropertiesPanel'
import TemplateSelector from '@/components/architecture/TemplateSelector'
import LoadImageDialog from '@/components/architecture/LoadImageDialog'
import { CloudService, ArchitectureNode, ArchitectureEdge, Architecture, CostAnalysis } from '@/types/architecture'

// Icons
import {
  Save,
  Upload,
  Download,
  FileImage,
  Zap,
  Trash2,
  Settings,
  Eye,
  EyeOff,
  Maximize,
  ChevronDown,
  Layers,
  Grid,
  Sparkles
} from 'lucide-react'

// API and utilities
import { architectureApi } from '@/lib/api'
import { vpcApi } from '@/lib/api'
import { optimizeArchitectureLayout } from '@/components/architecture/utils/layoutOptimizer'
import { validateArchitectureData } from '@/components/architecture/utils/architectureValidator'

export default function EnhancedArchitectureDesigner() {
  // Core state
  const [isDesignMode, setIsDesignMode] = useState(false)
  const [nodes, setNodes] = useState<ArchitectureNode[]>([])
  const [edges, setEdges] = useState<ArchitectureEdge[]>([])
  const [selectedNode, setSelectedNode] = useState<ArchitectureNode | null>(null)
  const [architectureName, setArchitectureName] = useState('Untitled Architecture')
  const [costAnalysis, setCostAnalysis] = useState<CostAnalysis | null>(null)
  
  // UI state
  const [activeTab, setActiveTab] = useState<'properties' | 'cost' | 'selective' | 'vpc'>('properties')
  const [showTemplateSelector, setShowTemplateSelector] = useState(false)
  const [showLoadImageDialog, setShowLoadImageDialog] = useState(false)
  const [useFileSystemAPI, setUseFileSystemAPI] = useState(true)
  const [triggerFitView, setTriggerFitView] = useState(false)
  
  // Enhanced UI state
  const [isPaletteCollapsed, setIsPaletteCollapsed] = useState(false)
  const [isPropertiesPanelVisible, setIsPropertiesPanelVisible] = useState(true)
  const [isPropertiesPanelMinimized, setIsPropertiesPanelMinimized] = useState(false)
  const [propertiesPanelPosition, setPropertiesPanelPosition] = useState({ x: 50, y: 100 })
  const [isLoadImageOptimized, setIsLoadImageOptimized] = useState(false)
  
  const { toast } = useToast()
  const location = useLocation()

  // Load generated architecture from requirements if available
  useEffect(() => {
    const loadGeneratedArchitecture = async () => {
      console.log('Enhanced ArchitectureDesigner useEffect triggered')
      console.log('Location state:', location.state)

      // Check if we came from requirements page with generated architecture
      if (location.state?.fromRequirements && location.state?.architectureData) {
        const architectureData = location.state.architectureData
        console.log('Loading architecture from requirements:', architectureData)

        try {
          const { nodes: validatedNodes, edges: validatedEdges } = validateArchitectureData(
            architectureData.nodes || [],
            architectureData.edges || []
          )

          if (validatedNodes.length === 0) {
            console.warn('No valid nodes found in architecture data')
            return
          }

          // Apply complete layout optimization to generated nodes and edges
          const { nodes: optimizedNodes, edges: optimizedEdges } = optimizeArchitectureLayout(validatedNodes, validatedEdges)

          setNodes(optimizedNodes)
          setEdges(optimizedEdges)
          setIsDesignMode(true)
          setArchitectureName('Generated Architecture')
          setIsLoadImageOptimized(true) // Enable Load Image optimization
          setTriggerFitView(true)

          // Show properties panel for generated architectures
          setIsPropertiesPanelVisible(true)
          setIsPropertiesPanelMinimized(false)

          toast({
            title: "Architecture Loaded",
            description: `Generated architecture loaded with ${validatedNodes.length} services and ${validatedEdges.length} connections.`,
          })
        } catch (error) {
          console.error('Error loading generated architecture:', error)
          toast({
            title: "Error",
            description: "Failed to load generated architecture.",
            variant: "destructive",
          })
        }
      }

      // Check for stored architecture from sessionStorage
      const storedArchitecture = sessionStorage.getItem('generatedArchitecture')
      if (storedArchitecture) {
        try {
          const architectureData = JSON.parse(storedArchitecture)
          console.log('Loading stored architecture:', architectureData)

          const { nodes: validatedNodes, edges: validatedEdges } = validateArchitectureData(
            architectureData.nodes || [],
            architectureData.edges || []
          )

          if (validatedNodes.length > 0) {
            const { nodes: optimizedNodes, edges: optimizedEdges } = optimizeArchitectureLayout(validatedNodes, validatedEdges)

            setNodes(optimizedNodes)
            setEdges(optimizedEdges)
            setIsDesignMode(true)
            setArchitectureName('Generated Architecture')
            setIsLoadImageOptimized(true)
            setTriggerFitView(true)

            // Clear from sessionStorage after loading
            sessionStorage.removeItem('generatedArchitecture')

            toast({
              title: "Architecture Loaded",
              description: `Generated architecture loaded with ${validatedNodes.length} services and ${validatedEdges.length} connections.`,
            })
          }
        } catch (error) {
          console.error('Error parsing stored architecture:', error)
          sessionStorage.removeItem('generatedArchitecture')
        }
      }
    }

    loadGeneratedArchitecture()
  }, [location.state, toast])

  // Enhanced node management
  const handleAddNode = useCallback((service: CloudService, position: { x: number; y: number }) => {
    const newNode: ArchitectureNode = {
      id: `${service.id}-${Date.now()}`,
      type: service.id === 'user' ? 'user' : 'service',
      position,
      data: {
        service,
        label: service.name,
        cost: 0,
        latency: 0,
        parameters: {},
      },
      selected: false,
    }

    setNodes(prev => [...prev, newNode])
    console.log('Added node:', newNode)
  }, [])

  const handleAddUser = useCallback(() => {
    const userService: CloudService = {
      id: 'user',
      name: 'User',
      provider: 'Generic',
      category: 'User',
      icon: '',
      fallbackIcon: '👤',
      description: 'End user or client'
    }

    const position = { x: 100, y: 100 }
    handleAddNode(userService, position)
  }, [handleAddNode])

  // Architecture management functions
  const handleStartFromScratch = async () => {
    setIsDesignMode(true)
    setNodes([])
    setEdges([])
    setSelectedNode(null)
    setArchitectureName('Untitled Architecture')
    setIsLoadImageOptimized(false)

    // Show properties panel when starting design
    setIsPropertiesPanelVisible(true)
    setIsPropertiesPanelMinimized(false)

    // Clear all VPCs when starting from scratch
    try {
      await vpcApi.clearAllVPCs()
    } catch (error) {
      console.error('Error clearing VPCs:', error)
    }

    toast({
      title: "Canvas Ready",
      description: "You can now start designing your architecture by dragging components to the canvas.",
    })
  }

  const handleLoadTemplate = () => {
    setShowTemplateSelector(true)
  }

  const handleClearCanvas = () => {
    setNodes([])
    setEdges([])
    setSelectedNode(null)
    setCostAnalysis(null)
    setArchitectureName('Untitled Architecture')
    setIsLoadImageOptimized(false)

    toast({
      title: "Canvas Cleared",
      description: "All components have been removed from the canvas.",
    })
  }

  // Enhanced node and edge change handlers
  const onNodesChange = useCallback((changes: any) => {
    setNodes((nds) => {
      const updatedNodes = nds.map(node => {
        const change = changes.find((c: any) => c.id === node.id)
        if (change) {
          if (change.type === 'position' && change.position) {
            return { ...node, position: change.position }
          }
          if (change.type === 'select') {
            return { ...node, selected: change.selected }
          }
          if (change.type === 'remove') {
            return null
          }
        }
        return node
      }).filter(Boolean) as ArchitectureNode[]

      return updatedNodes
    })
  }, [])

  const onEdgesChange = useCallback((changes: any) => {
    setEdges((eds) => {
      const updatedEdges = eds.map(edge => {
        const change = changes.find((c: any) => c.id === edge.id)
        if (change) {
          if (change.type === 'select') {
            return { ...edge, selected: change.selected }
          }
          if (change.type === 'remove') {
            return null
          }
        }
        return edge
      }).filter(Boolean) as ArchitectureEdge[]

      return updatedEdges
    })
  }, [])

  const onConnect = useCallback((connection: any) => {
    const newEdge: ArchitectureEdge = {
      id: `edge-${connection.source}-${connection.target}-${Date.now()}`,
      source: connection.source,
      target: connection.target,
      type: 'enhancedStep',
      animated: false,
      style: {
        stroke: '#2563EB',
        strokeWidth: 3,
        strokeDasharray: '0',
        strokeLinecap: 'round',
        strokeLinejoin: 'round'
      },
      markerEnd: {
        type: 'arrowclosed',
        color: '#2563EB',
        width: 26,
        height: 26
      },
      data: {
        showDirection: true,
        strokeColor: '#2563EB',
        strokeWidth: 3
      }
    }

    setEdges((eds) => [...eds, newEdge])

    toast({
      title: "Connection Created",
      description: "Services have been connected successfully.",
    })
  }, [])

  // Additional handlers for enhanced functionality
  const handleUpdateNode = useCallback((nodeId: string, updates: Partial<ArchitectureNode>) => {
    setNodes(prev => prev.map(node =>
      node.id === nodeId ? { ...node, ...updates } : node
    ))
  }, [])

  const handleDeleteNode = useCallback((nodeId: string) => {
    setNodes(prev => prev.filter(node => node.id !== nodeId))
    setEdges(prev => prev.filter(edge => edge.source !== nodeId && edge.target !== nodeId))
    setSelectedNode(null)

    toast({
      title: "Component Deleted",
      description: "Component and its connections have been removed.",
    })
  }, [toast])

  const handleCalculateCost = useCallback((nodeId: string) => {
    const node = nodes.find(n => n.id === nodeId)
    if (node) {
      // Implement cost calculation logic here
      console.log('Calculate cost for node:', node.data.service.name)

      toast({
        title: "Cost Calculated",
        description: `Cost calculated for ${node.data.service.name}`,
      })
    }
  }, [nodes, toast])

  const handleCostCalculated = useCallback((analysis: CostAnalysis) => {
    setCostAnalysis(analysis)

    toast({
      title: "Cost Analysis Complete",
      description: `Total estimated cost: $${analysis.totalCost.toFixed(2)}`,
    })
  }, [toast])

  // Template and file handling
  const handleTemplateSelect = useCallback((template: any) => {
    setNodes(template.nodes || [])
    setEdges(template.edges || [])
    setIsDesignMode(true)
    setArchitectureName(template.name || 'Template Architecture')
    setTriggerFitView(true)
    setShowTemplateSelector(false)

    toast({
      title: "Template Loaded",
      description: `${template.name} template has been loaded.`,
    })
  }, [toast])

  const handleArchitectureLoaded = useCallback((architecture: any) => {
    setNodes(architecture.nodes || [])
    setEdges(architecture.edges || [])
    setIsDesignMode(true)
    setIsLoadImageOptimized(true)
    setTriggerFitView(true)
    setArchitectureName(architecture.name || 'Loaded from Image')
    setShowLoadImageDialog(false)

    // Show properties panel for loaded architectures
    setIsPropertiesPanelVisible(true)
    setIsPropertiesPanelMinimized(false)

    toast({
      title: "Architecture Loaded",
      description: "Architecture has been loaded from the image.",
    })
  }, [toast])

  // Current architecture for panels
  const currentArchitecture: Architecture = {
    id: 'current',
    name: architectureName,
    nodes,
    edges,
    metadata: {
      totalCost: nodes.reduce((sum, node) => sum + (node.data.cost || 0), 0),
      totalLatency: Math.max(...nodes.map(node => node.data.latency || 0)),
      archType: 'Microservices',
      createdAt: new Date().toISOString(),
      description: `Architecture with ${nodes.length} components and ${edges.length} connections`
    }
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50 overflow-hidden">
      {/* Enhanced Header */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200 shadow-sm">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Sparkles className="h-5 w-5 text-purple-600" />
                <h1 className="text-lg font-semibold text-gray-900">Enhanced Architecture Designer</h1>
                <Badge variant="outline" className="text-xs bg-purple-50 text-purple-700 border-purple-200">
                  Enhanced
                </Badge>
              </div>
              
              {isDesignMode && (
                <div className="flex items-center space-x-2">
                  <Input
                    value={architectureName}
                    onChange={(e) => setArchitectureName(e.target.value)}
                    className="w-64 h-8 text-sm"
                    placeholder="Architecture name..."
                  />
                  <Badge variant="secondary" className="text-xs">
                    {nodes.length} components
                  </Badge>
                  {edges.length > 0 && (
                    <Badge variant="secondary" className="text-xs">
                      {edges.length} connections
                    </Badge>
                  )}
                </div>
              )}
            </div>

            {/* Enhanced Controls */}
            <div className="flex items-center space-x-2">
              {/* View Controls */}
              <div className="flex items-center space-x-1 border-r pr-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsPaletteCollapsed(!isPaletteCollapsed)}
                  className="text-xs"
                >
                  <Layers className="h-4 w-4 mr-1" />
                  {isPaletteCollapsed ? 'Expand' : 'Collapse'}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsPropertiesPanelVisible(!isPropertiesPanelVisible)}
                  className="text-xs"
                >
                  {isPropertiesPanelVisible ? <EyeOff className="h-4 w-4 mr-1" /> : <Eye className="h-4 w-4 mr-1" />}
                  Properties
                </Button>
              </div>

              {/* Architecture Actions */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Grid className="h-4 w-4 mr-2" />
                    Architecture
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuItem onClick={handleLoadTemplate}>
                    <Upload className="h-4 w-4 mr-2" />
                    Load Template
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setShowLoadImageDialog(true)}>
                    <FileImage className="h-4 w-4 mr-2" />
                    Load from Image
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuCheckboxItem
                    checked={useFileSystemAPI}
                    onCheckedChange={setUseFileSystemAPI}
                  >
                    Show "Save As" dialog
                  </DropdownMenuCheckboxItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {isDesignMode && (
                <Button variant="outline" size="sm" onClick={handleClearCanvas}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - Maximized Layout */}
      <div className="flex-1 flex overflow-hidden">
        {/* Collapsible Component Palette */}
        <CollapsibleComponentPalette
          onServiceDragStart={(event, service) => {
            event.dataTransfer.setData('application/reactflow', JSON.stringify(service))
            event.dataTransfer.effectAllowed = 'move'
          }}
          onAddUser={handleAddUser}
          isCollapsed={isPaletteCollapsed}
          onToggleCollapse={() => setIsPaletteCollapsed(!isPaletteCollapsed)}
          className="flex-shrink-0"
        />

        {/* Enhanced Canvas Area */}
        <div className="flex-1 relative bg-white">
          {!isDesignMode ? (
            <div className="h-full flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50">
              <div className="text-center max-w-md mx-auto p-8">
                <div className="mb-8">
                  <Sparkles className="h-16 w-16 text-purple-600 mx-auto mb-4" />
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Enhanced Architecture Designer</h2>
                  <p className="text-gray-600 mb-6">
                    Design cloud architectures with maximized canvas space and improved Load Image results
                  </p>
                </div>
                <div className="space-y-3">
                  <Button
                    onClick={() => setShowLoadImageDialog(true)}
                    className="w-full px-6 py-3 text-base border-2 border-green-300 text-green-700 hover:bg-green-50 hover:border-green-400 transition-all duration-200"
                  >
                    <FileImage className="h-5 w-5 mr-2" />
                    Load from Image
                  </Button>
                  <Button
                    onClick={handleStartFromScratch}
                    className="w-full px-6 py-3 text-base bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    <Zap className="h-5 w-5 mr-2" />
                    Start Designing
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <EnhancedArchitectureCanvas
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onNodeSelect={setSelectedNode}
              onAddNode={handleAddNode}
              triggerFitView={triggerFitView}
              optimizedForLoadImage={isLoadImageOptimized}
              className="w-full h-full"
            />
          )}
        </div>
      </div>

      {/* Floating Properties Panel */}
      {isDesignMode && (
        <FloatingPropertiesPanel
          selectedNode={selectedNode}
          architecture={currentArchitecture}
          costAnalysis={costAnalysis}
          activeTab={activeTab}
          isDesignMode={isDesignMode}
          isVisible={isPropertiesPanelVisible}
          isMinimized={isPropertiesPanelMinimized}
          position={propertiesPanelPosition}
          onUpdateNode={handleUpdateNode}
          onDeleteNode={handleDeleteNode}
          onCalculateCost={handleCalculateCost}
          onCostCalculated={handleCostCalculated}
          onTabChange={setActiveTab}
          onClose={() => setIsPropertiesPanelVisible(false)}
          onToggleMinimize={() => setIsPropertiesPanelMinimized(!isPropertiesPanelMinimized)}
          onPositionChange={setPropertiesPanelPosition}
        />
      )}

      {/* Dialogs */}
      {showTemplateSelector && (
        <TemplateSelector
          onClose={() => setShowTemplateSelector(false)}
          onTemplateSelect={handleTemplateSelect}
        />
      )}

      {showLoadImageDialog && (
        <LoadImageDialog
          onClose={() => setShowLoadImageDialog(false)}
          onArchitectureLoaded={handleArchitectureLoaded}
        />
      )}
    </div>
  )
}
