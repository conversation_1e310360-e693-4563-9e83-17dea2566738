import React, { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import PropertiesPanel from './PropertiesPanel'
import CostCalculationPanel from './CostCalculationPanel'
import SelectiveCostCalculation from './SelectiveCostCalculation'
import VPCManagement from './VPCManagement'
import { ArchitectureNode, Architecture, CostAnalysis } from '@/types/architecture'
import { 
  Settings, 
  DollarSign, 
  Calculator, 
  Network, 
  X, 
  Minimize2, 
  Maximize2,
  Move
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface FloatingPropertiesPanelProps {
  selectedNode: ArchitectureNode | null
  architecture: Architecture
  costAnalysis: CostAnalysis | null
  activeTab: 'properties' | 'cost' | 'selective' | 'vpc'
  isDesignMode: boolean
  isVisible: boolean
  isMinimized: boolean
  position: { x: number; y: number }
  onUpdateNode: (nodeId: string, updates: Partial<ArchitectureNode>) => void
  onDeleteNode: (nodeId: string) => void
  onCalculateCost: (nodeId: string) => void
  onCostCalculated: (analysis: CostAnalysis) => void
  onTabChange: (tab: 'properties' | 'cost' | 'selective' | 'vpc') => void
  onClose: () => void
  onToggleMinimize: () => void
  onPositionChange: (position: { x: number; y: number }) => void
  className?: string
}

export const FloatingPropertiesPanel: React.FC<FloatingPropertiesPanelProps> = ({
  selectedNode,
  architecture,
  costAnalysis,
  activeTab,
  isDesignMode,
  isVisible,
  isMinimized,
  position,
  onUpdateNode,
  onDeleteNode,
  onCalculateCost,
  onCostCalculated,
  onTabChange,
  onClose,
  onToggleMinimize,
  onPositionChange,
  className
}) => {
  const [isDragging, setIsDragging] = useState(false)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    setDragOffset({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    })
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      onPositionChange({
        x: e.clientX - dragOffset.x,
        y: e.clientY - dragOffset.y
      })
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging, dragOffset])

  if (!isVisible) return null

  const panelWidth = isMinimized ? 300 : 400
  const panelHeight = isMinimized ? 60 : 500

  return (
    <Card 
      className={cn(
        'fixed z-50 bg-white shadow-2xl border-2 border-gray-200 rounded-lg overflow-hidden',
        isDragging && 'cursor-grabbing',
        className
      )}
      style={{
        left: position.x,
        top: position.y,
        width: panelWidth,
        height: panelHeight,
        maxHeight: '80vh',
        maxWidth: '90vw'
      }}
    >
      {/* Header with drag handle */}
      <CardHeader 
        className={cn(
          'pb-2 bg-gradient-to-r from-blue-50 to-purple-50 border-b cursor-grab active:cursor-grabbing',
          isDragging && 'cursor-grabbing'
        )}
        onMouseDown={handleMouseDown}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Move className="h-4 w-4 text-gray-400" />
            <CardTitle className="text-sm font-medium text-gray-900">
              {isMinimized ? 'Properties Panel' : 'Architecture Properties'}
            </CardTitle>
            {selectedNode && !isMinimized && (
              <Badge variant="outline" className="text-xs">
                {selectedNode.data.service.name}
              </Badge>
            )}
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleMinimize}
              className="w-6 h-6 p-0"
            >
              {isMinimized ? <Maximize2 className="h-3 w-3" /> : <Minimize2 className="h-3 w-3" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="w-6 h-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardHeader>

      {/* Content - only show when not minimized */}
      {!isMinimized && (
        <CardContent className="flex-1 overflow-hidden p-0">
          {!isDesignMode ? (
            <div className="h-full flex items-center justify-center p-6">
              <div className="text-center text-gray-500">
                <p className="text-sm">Start designing to access properties and cost analysis</p>
              </div>
            </div>
          ) : (
            <Tabs value={activeTab} onValueChange={onTabChange} className="h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-4 bg-gray-50 m-2 mb-0">
                <TabsTrigger value="properties" className="text-xs">
                  <Settings className="h-3 w-3 mr-1" />
                  Properties
                </TabsTrigger>
                <TabsTrigger value="cost" className="text-xs">
                  <DollarSign className="h-3 w-3 mr-1" />
                  Cost
                </TabsTrigger>
                <TabsTrigger value="selective" className="text-xs">
                  <Calculator className="h-3 w-3 mr-1" />
                  Selective
                </TabsTrigger>
                <TabsTrigger value="vpc" className="text-xs">
                  <Network className="h-3 w-3 mr-1" />
                  VPC
                </TabsTrigger>
              </TabsList>

              <div className="flex-1 overflow-hidden">
                <TabsContent value="properties" className="h-full m-0">
                  <PropertiesPanel
                    selectedNode={selectedNode}
                    onUpdateNode={onUpdateNode}
                    onDeleteNode={onDeleteNode}
                    onCalculateCost={onCalculateCost}
                    className="h-full"
                  />
                </TabsContent>

                <TabsContent value="cost" className="h-full m-0">
                  <div className="h-full overflow-y-auto p-4">
                    <CostCalculationPanel
                      architecture={architecture}
                      onCostCalculated={onCostCalculated}
                    />
                  </div>
                </TabsContent>

                <TabsContent value="selective" className="h-full m-0">
                  <SelectiveCostCalculation
                    architecture={architecture}
                    className="h-full"
                  />
                </TabsContent>

                <TabsContent value="vpc" className="h-full m-0">
                  <VPCManagement className="h-full" />
                </TabsContent>
              </div>
            </Tabs>
          )}
        </CardContent>
      )}
    </Card>
  )
}

export default FloatingPropertiesPanel
